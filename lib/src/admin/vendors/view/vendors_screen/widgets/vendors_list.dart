import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendor_details_screen/vendor_details_screen.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/widgets/vendor_card/vendor_card_widget.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/entrance_fader.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';

class VendorsList extends StatelessWidget {
  final List<VendorModel> vendors;

  const VendorsList({
    super.key,
    required this.vendors,
  });

  @override
  Widget build(BuildContext context) {
    if (vendors.isEmpty) {
      return Center(
        child: EmptyDataWidget(
          message: context.tr.noVendorsFound,
        ),
      );
    }

    return ListView.separated(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        itemBuilder: (context, index) {
          final vendor = vendors[index];

          return WidgetAnimator(
            delay: Duration(milliseconds: index * 15),
            child: GestureDetector(
              onTap: () {
                currentAdminVendor = vendors[index];
                context.to(const VendorDetailsScreen());
              },
              child: VendorCardWidget(
                vendor: vendor,
              ),
            ),
          );
        },
        separatorBuilder: (context, index) => context.mediumGap,
        itemCount: vendors.length);
  }
}
