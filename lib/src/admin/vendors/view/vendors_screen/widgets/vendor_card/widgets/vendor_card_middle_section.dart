import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/buttons/base_text_button.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';

import '../../../../../../../../generated/assets.dart';

class VendorCardMiddleSection extends StatelessWidget {
  final VendorModel vendor;

  const VendorCardMiddleSection({super.key, required this.vendor});

  @override
  Widget build(BuildContext context) {
    final phoneNumber = vendor.phone?.startsWith('01') == true
        ? '+2${vendor.phone}'
        : vendor.phone;

    final link =
        vendor.websiteLink ?? 'https://i2shop.store/${vendor.businessName}';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('${context.tr.website}: ', style: context.labelLarge),
            BaseTextButton(
                title: link,
                isZeroPadding: true,
                onTap: () {
                  openURL(link);
                }),
          ],
        ),
        if (vendor.phone != null || vendor.phone != '') ...[
          context.xSmallGap,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text('${context.tr.phoneNumber}: ',
                      style: context.labelLarge),
                  BaseTextButton(
                    title: vendor.phone ?? '',
                    isZeroPadding: true,
                    onTap: () {
                      openURL('tel:${vendor.phone}');
                    },
                  ),
                ],
              ),
              context.smallGap,
              InkWell(
                onTap: () {
                  openURL('https://wa.me/$phoneNumber');
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.all(2),
                    child: Image.asset(
                      Assets.iconsWhatsApp,
                      height: 25,
                      width: 25,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
        if (vendor.email != null || vendor.email != '') ...[
          context.xSmallGap,
          Row(
            children: [
              Text('${context.tr.email}: ', style: context.labelLarge),
              BaseTextButton(
                title: vendor.email ?? '',
                isZeroPadding: true,
                onTap: () {
                  openURL('mailto:${vendor.email}');
                },
              ),
            ],
          ),
        ]
      ],
    );
  }
}
