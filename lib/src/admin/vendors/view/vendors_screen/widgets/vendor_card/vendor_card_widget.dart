import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/widgets/vendor_card/widgets/vendor_card_bottom_section.dart'
    show VendorCardBottomSection;
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/widgets/vendor_card/widgets/vendor_card_middle_section.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/cached_images/main_cached_image.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:provider/provider.dart';

import '../../../../../../core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import '../../../../../../screens/auth/view_model/auth_view_model.dart';
import '../../../add_vendor/widgets/add_and_edit_vendor_screen.dart';

class VendorCardWidget extends HookWidget {
  final VendorModel vendor;

  const VendorCardWidget({super.key, required this.vendor});

  @override
  Widget build(BuildContext context) {
    final isActive = vendor.expireDate?.isAfter(DateTime.now()) ?? false
        ? context.tr.active
        : context.tr.expired;

    final remainingDays = vendor.expireDate?.difference(DateTime.now()).inDays;

    final days = remainingDays != null ? remainingDays + 1 : 0;

    final iconColor = context.isDark ? ColorManager.white : ColorManager.black;

    final showPricing = useState(vendor.showPricing);

    final authVM = context.read<AuthVM>();

    final paidAmount = vendor.paidAmount ?? 0;
    final pricingPlanAmount = vendor.pricing?.price ?? 0;

    final remainingAmount = pricingPlanAmount - paidAmount;

    final vendorCurrency =
        vendor.config?.currencies.firstOrNull?.currencyEn ?? 'L.E';

    return Container(
        decoration: BoxDecoration(
          color: ColorManager.textFieldColor(context),
          borderRadius: BorderRadius.circular(AppRadius.baseRadius),
        ),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.all(10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        CupertinoIcons.calendar,
                        color: iconColor,
                      ),
                      context.xSmallGap,
                      Text(
                        vendor.startDate?.formatDateToString ??
                            vendor.createdAt.formatDateToString,
                        style: context.hint.copyWith(
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    vendor.isFree
                        ? context.tr.active
                        : '$isActive ($days ${context.tr.days})',
                    style: context.whiteHint.copyWith(
                      color: isActive == context.tr.active || vendor.isFree
                          ? ColorManager.successColor
                          : ColorManager.errorColor,
                      fontSize: 13,
                    ),
                  ),
                  if (!vendor.isFree)
                    Row(
                      children: [
                        Icon(
                          CupertinoIcons.calendar_badge_minus,
                          color: iconColor,
                        ),
                        context.xSmallGap,
                        Text(
                          (vendor.expireDate?.formatDateToString ?? ''),
                          style: context.whiteHint.copyWith(
                            color: ColorManager.errorColor,
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            ExpansionTile(
              iconColor: ColorManager.primaryColor,
              collapsedIconColor: context.appTheme.primaryColorDark,
              childrenPadding: EdgeInsets.all(AppSpaces.smallPadding),
              tilePadding: EdgeInsets.symmetric(horizontal: 10),
              title: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          borderRadius:
                              BorderRadius.circular(AppRadius.baseRadius),
                          color: vendor.logo?.url == null ||
                                  vendor.logo!.url!.isEmpty
                              ? ColorManager.lightBlack
                              : ColorManager.grey,
                        ),
                        width: 60.w,
                        height: 50.h,
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(AppRadius.baseRadius),
                          child: vendor.logo?.url == null ||
                                  vendor.logo!.url!.isEmpty
                              ? Center(
                                  child: Text(
                                    vendor.name
                                            ?.substring(0, 1)
                                            .toUpperCase() ??
                                        '',
                                    style: context.title.copyWith(
                                      fontSize: 20.sp,
                                      color: ColorManager.white,
                                    ),
                                  ),
                                )
                              : BaseCachedImage(
                                  vendor.logo?.url ?? '',
                                  width: 60.w,
                                  height: 50.h,
                                  fit: BoxFit.cover,
                                  cachedImage: false,
                                ),
                        ),
                      ),
                      context.mediumGap,
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(vendor.name ?? '',
                                style: context.labelLarge
                                    .copyWith(fontWeight: FontWeight.w600)),
                            Row(
                              children: [
                                Text(vendor.businessType ?? '',
                                    style: context.labelMedium.copyWith(
                                      fontWeight: FontWeight.w400,
                                    )),
                                Text(
                                  ' (${vendor.vendorType?.name})' ?? '',
                                  style: context.hint,
                                ),
                              ],
                            ),
                            if (remainingAmount > 0)
                              FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  '${context.tr.remainingAmount}: $remainingAmount $vendorCurrency',
                                  style: context.labelMedium.copyWith(
                                    color: ColorManager.errorColor,
                                  ),
                                ),
                              )
                            else if (paidAmount > 0)
                              FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  '${context.tr.paidAmount}: $paidAmount $vendorCurrency',
                                  style: context.labelMedium.copyWith(
                                    color: ColorManager.successColor,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () => context.to(AddEditVendorScreen(
                          vendor: vendor,
                        )),
                        child: CircleAvatar(
                          radius: 15.r,
                          backgroundColor: ColorManager.textFieldColor(context),
                          child: Icon(Icons.edit_rounded,
                              color: context.appTheme.primaryColorDark,
                              size: 18),
                        ),
                      ),
                    ],
                  ),
                  context.mediumGap,
                  VendorCardBottomSection(
                    vendor: vendor,
                  ),
                ],
              ),
              children: [
                // * Middle Section (Vendor Type, Remaining Amount - Phone - Email)
                VendorCardMiddleSection(
                  vendor: vendor,
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(context.tr.showPricing, style: context.labelLarge),
                    SwitchButtonWidget(
                      onChanged: (value) async {
                        currentAdminVendor = vendor;

                        showPricing.value = value;

                        final updatedVendor = vendor.config!.copyWith(
                          showPricing: showPricing.value,
                        );

                        await authVM.updateVendorConfig(
                          context,
                          config: updatedVendor,
                          updateCurrentVendor: false,
                        );

                        currentAdminVendor = null;
                      },
                      value: showPricing,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ));
  }
}
