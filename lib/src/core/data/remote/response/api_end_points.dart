import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';

class ApiEndPoints {
  //? Headers
  static const Map<String, String> headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static const String baseUrl = "https://backend.idea2app.tech/";

  static const String apiURL = "${baseUrl}api/";

  // static const String apiURL = "http://********:1337/api/"; //? Local Host for Android Emulator

  static const String productsCategory = "product-categories";
  static const String mainCategories = "main-categories";
  static const String products = "products";
  static String orders = "orders";
  static String ordersUsers = "orders-users";
  static const String users = "users";
  static final String cities =
      "cities?country.id_in=${VendorModelHelper.currentVendorCountryId()}";
  static const String vendors = "vendors";
  static const String settings = "setting";
  static const String banners = "banners";
  static const String subscriptionRequests = "subscription-requests";
  static const String notifications = "notifications";
  static const String currencies = "currencies";
  static const String countries = "countries";
  static const String configs = "configs";
  static const String payment = "payments";
  static const String shipping = "shippings";
  static const String expenses = "expenses";
  static const String pricing = "pricing";
  static const String tasks = "tasks";
  static const String uploadFile = "upload";
  static const String ordersStatistics = "orders-statistics";

  // reports
  static const String reports = "orders-report";

  static const String promoCodes = "promo-codes";

  static String webHookByInvoiceId(int? invoiceId) =>
      "webhooks?invoice_id=$invoiceId";

  static String webHooks = "webhooks?pagination[limit]=500";

  static String get orderCount {
    final currentAdminVendorStartDate =
        currentAdminVendor?.startDate ?? currentAdminVendor?.createdAt;

    final currentAdminVendorExpireDate =
        currentAdminVendor?.expireDate ?? DateTime.now().toUtc();

    // ? ---------------------------------------------------------------------
    final vendorStartDate = VendorModelHelper.currentVendorModel().startDate ??
        VendorModelHelper.currentVendorModel().createdAt;

    final vendorExpireDate = VendorModelHelper.currentVendorModel().expireDate;

    // ? ---------------------------------------------------------------------

    final startDate = vendorStartDate ?? currentAdminVendorStartDate;

    final expireDate = vendorExpireDate ?? currentAdminVendorExpireDate;

    if (startDate == null) {
      return '';
    }

    // ? ---------------------------------------------------------------------

    return "orders-count?filters[createdAt][\$gte]=${startDate?.toUtc()}&filters[createdAt][\$lte]=$expireDate";
  }
}
