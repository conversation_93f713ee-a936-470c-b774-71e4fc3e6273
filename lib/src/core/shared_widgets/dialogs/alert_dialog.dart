import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:lottie/lottie.dart';

import '../../resources/theme/theme.dart';

class AlertDialogWidget extends StatelessWidget {
  final Widget child;
  final bool isWarningMessage;
  final String? header;
  final Function() onConfirm;
  final bool isLoading;

  const AlertDialogWidget(
      {super.key,
      required this.child,
      this.isWarningMessage = false,
      this.isLoading = false,
      required this.onConfirm,
      this.header});

  @override
  Widget build(BuildContext context) {
    final title = header ?? context.tr.confirmation;

    return BaseDialog(
      isLoading: isLoading,
      radius: AppRadius.baseContainerRadius,
      backgroundColor: context.appTheme.cardColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //! Header text
          Row(
            children: [
              if (isWarningMessage) ...[
                SizedBox(
                    height: 50,
                    width: 50,
                    child: Lottie.asset(Assets.animatedAlert)),
                context.smallGap,
              ],
              Text(
                title,
                style: context.title.copyWith(
                    color: isWarningMessage
                        ? ColorManager.red
                        : ColorManager.primaryColor),
              ),
            ],
          ),

          context.mediumGap,

          //! Widget
          child,

          context.mediumGap,

          //? Buttons ==============================
          //! Confirm Button
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              MaterialButton(
                elevation: 0,
                textColor: Theme.of(context).focusColor,
                onPressed: onConfirm,
                child: Text(context.tr.confirm,
                    style: context.labelMedium.copyWith(
                        color: isWarningMessage
                            ? ColorManager.red
                            : ColorManager.primaryColor)),
              ),
              //! Dismiss Button
              MaterialButton(
                elevation: 0,
                textColor: ColorManager.primaryColor,
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  context.tr.dismiss,
                  style: context.labelMedium.copyWith(
                      color: context.isDark
                          ? ColorManager.grey.withOpacity(0.4)
                          : ColorManager.darkGrey.withOpacity(0.4)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
