import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/icon_widget/icon_widget.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/bottom_nav_provider.dart';
import 'package:provider/provider.dart';

import '../../../screens/categories/view_model/main_category_view_model.dart';

class BottomNavBarWidget extends StatelessWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final mainCategoryVM = context.read<MainCategoryVM>();

    Widget bottomNavIcon(String iconPath, {bool isSelected = false}) {
      return IconWidget(
        icon: iconPath,
        height: 30,
        width: 50,
        color: isSelected
            ? context.isDark
                ? ColorManager.white
                : ColorManager.black
            : Color(0xffB2B2B2),
      );
    }

    return Consumer<BottomNavbarVM>(
      builder: (context, bottomNavbarVM, child) {
        return BottomNavigationBar(
          selectedItemColor:
              context.isDark ? ColorManager.white : ColorManager.black,
          unselectedItemColor: Color(0xffB2B2B2),
          currentIndex: bottomNavbarVM.currentIndex,
          onTap: (index) {
            bottomNavbarVM.setCurrentIndex(index);
            mainCategoryVM.setTabIndex(0);
          },
          backgroundColor:
              context.isDark ? Color(0xff3A3A3A) : ColorManager.white,
          iconSize: 24.r,
          elevation: 0,
          selectedFontSize: 12,
          unselectedFontSize: 12,
          type: BottomNavigationBarType.fixed,
          selectedLabelStyle: TextStyle(fontWeight: FontWeight.bold),
          items: [
            BottomNavigationBarItem(
              icon: Padding(
                  padding:
                      const EdgeInsets.only(bottom: AppSpaces.smallPadding),
                  child: bottomNavIcon(Assets.iconsHomeNav,
                      isSelected: bottomNavbarVM.currentIndex == 0)),
              label: context.tr.home,
            ),
            BottomNavigationBarItem(
              icon: Padding(
                padding: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
                child: bottomNavIcon(Assets.iconsCategoryNav,
                    isSelected: bottomNavbarVM.currentIndex == 1),
              ),
              label: context.tr.categories,
            ),
            BottomNavigationBarItem(
              icon: Padding(
                  padding:
                      const EdgeInsets.only(bottom: AppSpaces.smallPadding),
                  child: bottomNavIcon(Assets.iconsOrdersNav,
                      isSelected: bottomNavbarVM.currentIndex == 2)),
              label: context.tr.orders,
            ),
            BottomNavigationBarItem(
              icon: Padding(
                  padding:
                      const EdgeInsets.only(bottom: AppSpaces.smallPadding),
                  child: bottomNavIcon(Assets.iconsDashboardNav,
                      isSelected: bottomNavbarVM.currentIndex == 3)),
              label: context.tr.dashboard,
            ),
          ],
        );
      },
    );
  }
}
