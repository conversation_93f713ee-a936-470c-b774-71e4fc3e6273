import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

class SwitchButtonWidget extends StatelessWidget {
  final ValueNotifier<bool> value;
  final Function(bool) onChanged;
  final String? title;

  const SwitchButtonWidget(
      {super.key, required this.value, required this.onChanged, this.title});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          context.isEng ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: context.greyLabelLarge,
          ),
        ],
        Switch.adaptive(
            padding: const EdgeInsets.all(0),
            trackOutlineColor: value.value
                ? const WidgetStatePropertyAll(Colors.transparent)
                : const WidgetStatePropertyAll(ColorManager.grey),
            trackOutlineWidth: const WidgetStatePropertyAll(1),
            hoverColor: Colors.blue,
            activeTrackColor: Colors.green,
            thumbColor: const WidgetStatePropertyAll(Colors.white),
            inactiveTrackColor: Colors.grey.withOpacity(0.5),
            value: value.value,
            onChanged: onChanged),
      ],
    );
  }
}
