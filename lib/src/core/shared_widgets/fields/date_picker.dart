import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:intl/intl.dart';

class BaseDatePicker extends HookWidget {
  final ValueNotifier<DateTime?> selectedDateNotifier;
  final String label;
  final void Function(DateTime?)? onChanged;
  final bool isRequired;
  final String? title;

  const BaseDatePicker({
    super.key,
    this.onChanged,
    required this.selectedDateNotifier,
    required this.label,
    this.title,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget errorText(String? errorText) {
      if (errorText == null) {
        return const SizedBox.shrink();
      }
      return Padding(
        padding: const EdgeInsets.only(left: 12, bottom: 8.0),
        child: Text(
          errorText,
          style: TextStyle(
            color: Theme.of(context).colorScheme.error,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: context.labelLarge,
          ),
          context.smallGap,
        ],
        ValueListenableBuilder(
          valueListenable: selectedDateNotifier,
          builder: (context, value, child) {
            final emptyDate = value == null;

            final selectedDate = value;

            return FormField(
              validator: (value) {
                if (isRequired && emptyDate) {
                  return "Please select $label";
                }
                return null;
              },
              builder: (state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          locale: Localizations.localeOf(context),
                          context: context,
                          initialDate: selectedDate == null ||
                                  selectedDate.isBefore(DateTime(2000))
                              ? DateTime.now()
                              : selectedDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                          builder: (BuildContext context, Widget? child) {
                            return child!;
                          },
                        );
                        if (date == null) return;
                        selectedDateNotifier.value = date;

                        if (onChanged != null) onChanged!(date);
                        state.didChange(date);
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: Row(
                          children: [
                            CircleAvatar(
                              backgroundColor: context.isDark
                                  ? Colors.grey.withOpacity(.1)
                                  : Colors.grey.withOpacity(0.15),
                              child: const Icon(
                                Icons.calendar_today,
                              ),
                            ),
                            context.smallGap,
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: Text(
                                            emptyDate
                                                ? "Select $label"
                                                : DateFormat('yyyy-MM-dd', 'en')
                                                    .format(selectedDate!),
                                            style: emptyDate
                                                ? context.labelMedium
                                                : context.labelLarge,
                                            maxLines: 1,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    errorText(state.errorText),
                  ],
                ).decorated(
                  color: context.isDark
                      ? Colors.grey.withOpacity(.1)
                      : Colors.grey.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(
                      AppRadius.extraLargeContainerRadius),
                );
              },
            );
          },
        ),
      ],
    );
  }
}
