part of shared_widgets;

class _ViewNetworkImagesList extends StatelessWidget {
  final ValueNotifier<List<BaseMediaModel>>? networkImages;
  final Function(String url) onRemoveImage;
  final List<String>? pickedResult;

  const _ViewNetworkImagesList(
      {required this.networkImages,
      required this.onRemoveImage,
      this.pickedResult});

  @override
  Widget build(BuildContext context) {
    final pickResultIsNotNull =
        pickedResult != null && pickedResult!.isNotEmpty;

    final showAddImageButton = !pickResultIsNotNull;

    return ListView(
      shrinkWrap: true,
      scrollDirection: Axis.horizontal,
      children: [
        ValueListenableBuilder(
          valueListenable: networkImages!,
          builder: (context, value, child) {
            return Row(
              children: value.indexed.map((networkImage) {
                final index = networkImage.$1;
                final image = networkImage.$2;

                return Row(
                  children: [
                    Stack(
                      alignment: Alignment.topRight,
                      children: [
                        ClipRRect(
                          borderRadius:
                              BorderRadius.circular(AppRadius.baseRadius),
                          child: Image.network(
                            image.url ?? '',
                            height: 90.h,
                            width: 110.w,
                            fit: BoxFit.cover,
                          ),
                        ),
                        CircleAvatar(
                                radius: 15,
                                backgroundColor: Colors.red,
                                child: IconButton(
                                  onPressed: () {
                                    onRemoveImage(
                                        networkImages!.value[index].url!);

                                    networkImages!.value.removeAt(index);
                                  },
                                  icon: const Icon(Icons.close,
                                      size: 15, color: Colors.white),
                                ))
                            .paddingSymmetric(
                                vertical: AppSpaces.xSmallPadding,
                                horizontal: AppSpaces.xSmallPadding),
                      ],
                    ),
                    if (index == value.length - 1 && showAddImageButton) ...[
                      context.mediumGap,
                      _PickImageButton(
                        allowMultiple: true,
                        oldFilesLength: networkImages!.value.length,
                      ).sized(
                        height: 90.h,
                        width: 110.w,
                      )
                    ]
                  ],
                ).paddingSymmetric(horizontal: AppSpaces.smallPadding);
              }).toList(),
            );
          },
        ),

        // //! View Picked Image ========================================
        _ViewPickedImages(
          images: pickedResult!,
          oldFilesLength: networkImages?.value.length ?? 0,
        )
      ],
    ).sized(height: 110.h);
  }
}
