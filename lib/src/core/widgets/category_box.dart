import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/styles/styles.dart';

class CategoryBox extends StatelessWidget {
  final List children;
  final Widget suffix;
  final String title;

  const CategoryBox({
    super.key,
    required this.suffix,
    required this.children,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: Styles.defaultBorderRadius,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(Styles.defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                suffix
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }
}
