import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

import '../../../../../../../generated/assets.dart';
import '../../../../../../core/shared_widgets/shared_widgets.dart';

class PromoCodesActionButton extends StatelessWidget {
  final bool isEdit;
  final Function() onTap;

  const PromoCodesActionButton(
      {super.key, this.isEdit = true, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final isDark = context.isDark;

    final icon = BaseLottieWidget.icon(
      isEdit ? 'assets/animated/edit.json' : Assets.animatedDelete,
      width: 20,
      height: 30,
    ).onTap(onTap);

    if (isDark) return icon;

    return icon
        .container(
            padding: const EdgeInsets.all(AppSpaces.smallPadding),
            decoration: const BoxDecoration(
              color: ColorManager.darkGrey,
              shape: BoxShape.circle,
            ))
        .onTap(onTap);
  }
}
