import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/promo_codes/view/widgets/add_promo_codes_dialog.dart';
import 'package:provider/provider.dart';

import '../../../core/shared_widgets/appbar/main_appbar.dart';
import '../controller/promo_codes_view_model.dart';
import 'widgets/promo_codes_list.dart';

class PromoCodesScreen extends HookWidget {
  const PromoCodesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final promoCodesVM = context.read<PromoCodesVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        promoCodesVM.getPromoCodes(context);
      });
      return () {};
    }, []);

    return Consumer<PromoCodesVM>(
      builder: (context, promoCodesVM, child) {
        return Scaffold(
            floatingActionButton: FloatingActionButton(
              backgroundColor: ColorManager.primaryColor,
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return const AddPromoCodesDialog();
                  },
                );
              },
              child: const Icon(
                Icons.add,
                color: ColorManager.white,
                size: 30,
              ),
            ),
            appBar: MainAppBar(
              title: currentAdminVendor != null
                  ? '${currentAdminVendor?.name} ${context.tr.promoCodes}'
                  : context.tr.promoCodes,
              haveBackButton: true,
            ),
            body: promoCodesVM.isLoading
                ? const Center(child: LoadingWidget())
                : PromoCodesList(
                    promoCodes: promoCodesVM.promoCodes,
                  ));
      },
    );
  }
}
