import 'dart:async';

import '../../../core/data/remote/app_exception.dart';
import '../../../core/data/remote/network/base_api_service.dart';
import '../../../core/data/remote/response/api_end_points.dart';
import '../model/promo_codes_model.dart';

class PromoCodesRepository {
  final BaseApiServices networkApiServices;

  PromoCodesRepository({required this.networkApiServices});

  Future<List<PromoCodesModel>> getPromoCodes() async {
    final response =
        await networkApiServices.getResponse(ApiEndPoints.promoCodes);

    final promoCodesData =
        (response as List).map((e) => PromoCodesModel.fromJson(e)).toList();

    return promoCodesData;
  }

  Future<void> addEditPromoCodes({
    required PromoCodesModel promoCodes,
    bool isEdit = false,
  }) async {
    try {
      if (isEdit) {
        await networkApiServices.putResponse(
          ApiEndPoints.promoCodes,
          data: promoCodes.toJson(),
          connectWithVendorRelation: false,
        );
      } else {
        await networkApiServices.postResponse(
          ApiEndPoints.promoCodes,
          data: promoCodes.toJson(),
          connectWithVendorRelation: false,
        );
      }
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  Future<void> editPromoCodesStatus(
      {required PromoCodesModel promoCodes}) async {
    try {
      await networkApiServices.putResponse(
        ApiEndPoints.promoCodes,
        data: promoCodes.toActiveJson(),
        connectWithVendorRelation: false,
      );
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  Future<void> deletePromoCodes({
    required PromoCodesModel promoCode,
  }) async {
    try {
      await networkApiServices.deleteResponse(
        '${ApiEndPoints.promoCodes}/${promoCode.documentId}',
      );
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }
}
