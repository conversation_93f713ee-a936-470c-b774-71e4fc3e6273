import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';

import '../../../core/data/remote/network/base_api_service.dart';
import '../../../core/data/remote/response/api_end_points.dart';
import '../model/banner_model.dart';

class BannerRepository {
  final BaseApiServices networkApiServices;

  BannerRepository({required this.networkApiServices});

  //! Get banner ====================================
  Future<List<BannerModel>> getBanner() async {
    try {
      dynamic response =
          await networkApiServices.getResponse(ApiEndPoints.banners);

      final bannerData = await compute(responseFromBannerModel, response);

      return bannerData;
    } catch (error) {
      rethrow;
    }
  }

  //! Add Banner ====================================
  Future<void> addBanner(
      {required BannerModel banner, required String fileResult}) async {
    try {
      await networkApiServices.postResponse(ApiEndPoints.banners,
          fieldName: "media", fileResult: [fileResult], data: banner.toJson());
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  //! Edit Banner ====================================
  Future<void> editBanner({
    required BannerModel banner,
    required String fileResult,
  }) async {
    try {
      await networkApiServices.putResponse(ApiEndPoints.banners,
          fieldName: "media", fileResult: [fileResult], data: banner.toJson());
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  //! Delete Banner ====================================
  Future<void> deleteBanner({required BannerModel banner}) async {
    try {
      await networkApiServices.deleteResponse(
        '${ApiEndPoints.banners}/${banner.documentId}',
      );

      await networkApiServices.deleteImage(imageId: banner.media!.id);
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  Future<void> deleteBannerByVendorId() async {
    try {
      final banners = await getBanner();
      for (var banner in banners) {
        await deleteBanner(banner: banner);
      }
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }
}
