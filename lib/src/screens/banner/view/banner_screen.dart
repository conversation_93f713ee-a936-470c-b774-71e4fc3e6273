import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view/widgets/add_banner_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view/widgets/banner_list.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view_model/banner_VM.dart';
import 'package:provider/provider.dart';

import '../../dashboard/view_model/extra_settings_view_model.dart';
import '../../shared/media/view_models/media_view_model.dart';

class BannerScreen extends HookWidget {
  const BannerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final bannerVM = context.read<BannerVM>();
    final extraSettingsVM = context.read<ExtraSettingVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        bannerVM.getBannersData(context);
        extraSettingsVM.getExtraSettings(context);
      });
      return () {};
    }, []);

    return Scaffold(
      appBar: MainAppBar(
        title: currentAdminVendor != null
            ? '${currentAdminVendor?.name} ${context.tr.banners}'
            : context.tr.banners,
        haveBackButton: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Expanded(child: BannerList()),
          Consumer2<BannerVM, ExtraSettingVM>(
            builder: (context, bannerVM, extraSettingVM, child) {
              final bannerLimit =
                  extraSettingsVM.extraSettings?.bannerLimit ?? 0;

              return Button(
                label: context.tr.addBanner,
                onPressed: () async {
                  if (bannerVM.bannerList.length >= bannerLimit) {
                    context.showBarMessage(context.tr.bannerLimitReached,
                        isError: true);
                    return;
                  } else {
                    await showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return const AddBannerDialog();
                      },
                    );
                  }

                  final mediaVM = context.read<MediaVM>();
                  mediaVM.clearFiles();
                },
              );
            },
          ).paddingAll(AppSpaces.largePadding)
        ],
      ),
    );
  }
}
