import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/city_cost_model.dart';
import 'package:provider/provider.dart';

import '../../../auth/view_model/auth_view_model.dart';

class AddAreasFromSuggestionsWidget extends HookWidget {
  final ValueNotifier<List<AreaModel>> selectedAreas;
  const AddAreasFromSuggestionsWidget({super.key, required this.selectedAreas});

  @override
  Widget build(BuildContext context) {
    final authVM = context.read<AuthVM>();
    final currentCurrency = authVM.currentCurrency(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        context.largeGap,
        Text(
          context.tr.selectedAreas,
          style: context.subHeadLine,
        ),
        context.mediumGap,
        ListView.separated(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: selectedAreas.value.length,
          separatorBuilder: (context, index) => context.mediumGap,
          itemBuilder: (context, index) {
            final area = selectedAreas.value[index];
            return SelectedSuggestionAreaCardWidget(
              area: area,
              currency: currentCurrency,
            );
          },
        ),
      ],
    );
  }
}

class SelectedSuggestionAreaCardWidget extends HookWidget {
  final AreaModel area;
  final String currency;

  const SelectedSuggestionAreaCardWidget({
    super.key,
    required this.area,
    required this.currency,
  });

  @override
  Widget build(BuildContext context) {
    final isActive = useState(area.isActive ?? true);
    final isFreeShipping = useState(area.isFreeShipping ?? false);
    final costController = useTextEditingController();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.appTheme.cardColor,
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
      ),
      padding: EdgeInsets.all(AppSpaces.mediumPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('${area.nameEn} - ${area.nameAr}', style: context.title),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(context.tr.active, style: context.labelLarge),
              SwitchButtonWidget(
                value: isActive,
                onChanged: (value) {
                  isActive.value = value;
                  area.isActive = value;
                },
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(context.tr.freeShipping, style: context.labelLarge),
              SwitchButtonWidget(
                value: isFreeShipping,
                onChanged: (value) {
                  isFreeShipping.value = value;
                  area.isFreeShipping = value;
                },
              ),
            ],
          ),
          BaseTextField(
            controller: costController,
            textInputType: TextInputType.number,
            hint: context.tr.cost,
            onChanged: (value) {
              area.cost = num.tryParse(value);
            },
            suffixIcon: Padding(
              padding: EdgeInsets.all(15),
              child: Text(currency, style: context.labelMedium),
            ),
          ),
        ],
      ),
    );
  }
}
