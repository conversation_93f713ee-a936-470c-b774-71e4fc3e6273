import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/icon_widget/icon_widget.dart';

import '../../../core/resources/app_spaces.dart';

class SettingsWidget extends StatelessWidget {
  final String? iconPath;
  final String header;
  final Widget? trailingWidget;
  final bool isArrow;
  final Function()? onTap;
  final double verticalPadding;
  final bool isWebViewFeature;
  final String? webViewIcon;
  final bool isDrawer;

  const SettingsWidget({
    super.key,
    this.iconPath,
    required this.header,
    this.trailingWidget,
    this.isArrow = true,
    this.isWebViewFeature = false,
    this.isDrawer = false,
    this.onTap,
    this.verticalPadding = AppSpaces.mediumPadding,
    this.webViewIcon,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (isWebViewFeature)
            SvgPicture.network(webViewIcon ?? '',
                width: 24.w,
                height: 24.h,
                fit: BoxFit.cover,
                color: ColorManager.primaryColor)
          else
            IconWidget(
                icon: iconPath ?? '',
                color: (isDrawer || !context.isDark)
                    ? ColorManager.primaryColor
                    : context.appTheme.primaryColorDark),
          context.mediumGap,
          Text(
            header,
            style: context.labelLarge,
          ),
          if (trailingWidget != null) ...[
            const Spacer(),
            trailingWidget!
          ] else ...[
            const Spacer(),
            if (isArrow) Icon(Icons.arrow_forward_ios_rounded, size: 13.r)
          ],
        ],
      ).paddingSymmetric(
          horizontal: AppSpaces.mediumPadding, vertical: verticalPadding),
    );
  }
}
