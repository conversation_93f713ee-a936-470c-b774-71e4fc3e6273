import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/drawer/widgets/settings_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';
import 'package:idea2app_vendor_app/src/screens/store/view/store_screen.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_plans/view/vendor_choose_plan_screen.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';

import '../../../admin/vendors/view/vendors_screen/vendors_screen.dart';
import '../../../core/resources/theme/theme.dart' show ColorManager;
import '../../../core/shared_widgets/loading/loading_widget.dart';
import '../../auth/models/helper_models/vendor_helper_model.dart';
import '../../gemini/view/gemini_screen.dart';
import '../../settings/view/settings_screen.dart';

class DrawerTiles extends HookWidget {
  const DrawerTiles({super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.watch<AuthVM>();
    void logout() {
      context.read<AuthVM>().logout(context);
    }

    final showPricing = authVM.currentVendor?.showPricing ?? true;

    final config = VendorModelHelper.currentVendorModel().config;

    final isActiveWorkingTime = config?.isActiveWorkingTime;

    final isActive = useState(isActiveWorkingTime ?? true);
    final advancedSettings =
        VendorModelHelper.currentVendorModel().vendorAdvanced.firstOrNull;
    final isAdmin = VendorModelHelper.isAdmin();

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              context.tr.storeActive,
              style: context.title,
            ),
            Row(
              children: [
                if (authVM.isLoading)
                  const LoadingWidget()
                else ...[
                  Text(
                    isActive.value ? context.tr.on : context.tr.off,
                    style: context.labelLarge
                        .copyWith(color: ColorManager.primaryColor),
                  ),
                  context.smallGap,
                  SwitchButtonWidget(
                    value: isActive,
                    onChanged: (value) {
                      isActive.value = value;

                      final copedConfig =
                          VendorModelHelper.currentVendorModel().config;

                      Log.f('copedConfig ${copedConfig?.toJson()}');

                      authVM.updateVendorConfig(
                        config: copedConfig!.copyWith(
                          isActiveWorkingTime: value,
                        ),
                        context,
                      );
                    },
                  ),
                ]
              ],
            ),
          ],
        ).paddingSymmetric(
          horizontal: AppSpaces.mediumPadding,
        ),

        Divider(),

        //! Change Plan
        if (showPricing)
          SettingsWidget(
            isDrawer: true,
            onTap: () => context.to(const VendorChoosePlanScreen()),
            header: context.tr.changePlan,
            iconPath: Assets.iconsPayment,
          ),
        //! Store Screen
        InkWell(
            onTap: () => context.to(const StoreScreen()),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Icon(
                  Icons.store_mall_directory_rounded,
                ),
                context.mediumGap,
                Text(
                  context.tr.store,
                  style: context.labelLarge,
                ),
                const Spacer(),
                Icon(Icons.arrow_forward_ios_rounded, size: 13.r)
              ],
            ).paddingSymmetric(
                horizontal: AppSpaces.mediumPadding,
                vertical: AppSpaces.mediumPadding)),

        //! Advanced Control
        if (advancedSettings != null)
          SettingsWidget(
            isDrawer: true,
            isWebViewFeature: true,
            onTap: () => openURL(advancedSettings.link),
            header:
                context.isEng ? advancedSettings.name : advancedSettings.nameAr,
            webViewIcon: advancedSettings.icon.url ?? '',
          ),

        //! Gemini || Vendors Screen
        SettingsWidget(
          isDrawer: true,
          onTap: () => context.to(isAdmin ? VendorsScreen() : GeminiScreen()),
          header: isAdmin ? 'Vendors' : context.tr.aiAssistant,
          iconPath: Assets.aiSvg,
        ),

        SettingsWidget(
          isDrawer: true,
          onTap: () => context.to(const SettingsScreen()),
          header: context.tr.appSettings,
          iconPath: Assets.iconsSettings,
        ),

        const Spacer(),

        //! Logout
        SettingsWidget(
          isDrawer: true,
          onTap: logout,
          header: context.tr.log_out,
          iconPath: Assets.iconsLogout,
        ).paddingSymmetric(vertical: AppSpaces.smallPadding),
        context.mediumGap
      ],
    );
  }
}
