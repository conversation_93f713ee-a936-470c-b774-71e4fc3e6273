import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/show_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';

import '../../../view_model/extra_settings_view_model.dart';

class ColorCard extends StatelessWidget {
  final ExtraSettingsModel color;
  final ExtraSettingVM extraSettingsVM;

  const ColorCard(
      {super.key, required this.color, required this.extraSettingsVM});

  @override
  Widget build(BuildContext context) {
    final radius = context.isEng
        ? const BorderRadius.only(
            topLeft: Radius.circular(AppRadius.baseRadius),
            bottomLeft: Radius.circular(AppRadius.baseRadius),
          )
        : const BorderRadius.only(
            topRight: Radius.circular(AppRadius.baseRadius),
            bottomRight: Radius.circular(AppRadius.baseRadius),
          );

    return Row(
      children: [
        Expanded(
          child: Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: Color(int.tryParse(color.englishName ?? '') ?? 0),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 1,
                  offset: const Offset(0, 1), // changes position of shadow
                ),
              ],
              borderRadius: radius,
            ),
          ),
        ),
        context.smallGap,
        _DeleteColorButton(
          extraSettingsVM: extraSettingsVM,
          color: color,
        ),
      ],
    );
  }
}

class _DeleteColorButton extends StatelessWidget {
  final ExtraSettingsModel color;
  final ExtraSettingVM extraSettingsVM;

  const _DeleteColorButton(
      {required this.color, required this.extraSettingsVM});

  @override
  Widget build(BuildContext context) {
    final radius = context.isEng
        ? const BorderRadius.only(
            bottomRight: Radius.circular(AppRadius.baseRadius),
            topRight: Radius.circular(AppRadius.baseRadius),
          )
        : const BorderRadius.only(
            bottomLeft: Radius.circular(AppRadius.baseRadius),
            topLeft: Radius.circular(AppRadius.baseRadius),
          );

    return Material(
      elevation: 1,
      borderRadius: radius,
      child: IconButton(
        onPressed: () {
          showAlertDialog(context,
              child: Text(context.tr.areYouSureYouWantToDeleteThisColor),
              onConfirm: () {
            extraSettingsVM.deleteColor(context, color: color);
          });
        },
        icon: const Icon(Icons.delete, color: Colors.red),
      ).decorated(
        height: 40.h,
        border: Border.all(
          color: Color(int.tryParse(color.englishName ?? '') ?? 0),
        ),
        borderRadius: radius,
      ),
    );
  }
}
