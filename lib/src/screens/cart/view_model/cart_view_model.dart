import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';
import 'package:idea2app_vendor_app/src/screens/store/local_repo/store_local_repo.dart';

import '../../../core/utils/logger.dart';

class CartVM extends BaseVM {
  final StoreLocalRepo storeLocalRepo;

  int counter = 0;
  List<ProductQuantityModel> cartList = [];

  CartVM({required this.storeLocalRepo});

  // int get total => cartList.fold(
  //     0,
  //         (previousValue, element) =>
  //     previousValue + (element.quantity * element.product.price!.toInt()));

  // int get totalQuantity => cartList.fold(
  //     0, (previousValue, element) => previousValue + element.quantity);

  //! Get Cart  ======================================
  Future<List<ProductQuantityModel>> getCart() async {
    cartList = await storeLocalRepo.getCart();

    counter = cartList.length;

    notifyListeners();

    return cartList;
  }

//! Add to cart ======================================
  Future<dynamic> saveProductToCart(
    BuildContext context, {
    required ProductQuantityModel productQuantity,
  }) async {
    return await baseFunction(context, () async {
       await storeLocalRepo.saveProductToCart(
          productQuantity: productQuantity);

      getCart();

      if (context.mounted) {
        context.back();
        context.showFlushBar(
          type: FlushBarType.add,
        );
      }
      notifyListeners();
    });
  }

//! Update Quantity ======================================
Future<bool> updateQuantity({
  required ProductQuantityModel productQuantity,
  bool isIncrease = true

}) async {
  try {
    await storeLocalRepo.updateQuantity(productQuantity: productQuantity, isIncrease: isIncrease);

    getCart();

    return true;
  } catch (e) {
    Log.e(' Error when you try to update quantity ${e.toString()}');

    return false;
  }
}

//! Delete Product From Cart  ======================================
  Future<void> deleteFromCart({required String id}) async {
    await storeLocalRepo.deleteFromCart(id: id);
    getCart();
    notifyListeners();
  }
//
//
// //! Current Cart ======================================
// CartModel currentCart(int productId) {
//   final cart =
//   cartList.firstWhere((element) => element.product.id == productId);
//
//   return cart;
// }
//
// //! Delete All Cart ===============================================
// Future<void> clearCart() async {
//   try {
//     await cartLocalRepository.clearCart();
//     getCart();
//     notifyListeners();
//   } catch (e) {
//     Log.e(' Error when you try to delete cart ${e.toString()}');
//   }
// }
}
