import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:lottie/lottie.dart';

import '../../../../core/shared_widgets/shared_widgets.dart';

class MainOnBoardingWidget extends StatelessWidget {
  final String image;
  final String title;
  final String subtitle;
  final String label;
  final BoxFit fit;
  final VoidCallback onTap;

  const MainOnBoardingWidget(
      {Key? key,
      context,
      required this.image,
      required this.title,
      required this.subtitle,
      this.label = 'S.of(context).next',
      required this.onTap,
      this.fit = BoxFit.contain})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Spacer(),
        SizedBox(
          height: 300.h,
          child: Lottie.asset(
            image,
            width: double.infinity,
            height: 300.h,
            fit: fit,
          ),
        ),
        Text(
          title,
          style: context.title,
        ),
        SizedBox(
          height: 10.h,
        ),
        Text(
          subtitle,
          textAlign: TextAlign.center,
          style: context.title,
        ),
        const Spacer(),
        SizedBox(
            width: ScreenUtil().screenWidth / 2,
            height: 45.h,
            child: Button(
              label: label,
              onPressed: onTap,
            )),
        const Spacer(),
      ],
    );
  }
}
