import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/app_spaces.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../view_model/payment_view_model.dart';

class AddPaymentDialog extends HookWidget {
  const AddPaymentDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final descriptionController = useTextEditingController();

    final paymentValue = useState<String?>(null);
    final formKey = useState(GlobalKey<FormState>());

    final isPhoneNumber = paymentValue.value == AppConsts.vodafoneCash ||
        paymentValue.value == AppConsts.etisalatCash ||
        paymentValue.value == AppConsts.orangeCash;

    final isBankAccount = paymentValue.value == AppConsts.bankAccount;
    final isInstapay = paymentValue.value == AppConsts.instapay;

    final phoneNumberOrBankAccount =
        paymentValue.value == AppConsts.bankAccount ||
            paymentValue.value == AppConsts.instapay;

    final hint = phoneNumberOrBankAccount
        ? '${context.tr.phoneNumber} ${context.tr.Or} ${context.tr.bankAccount}'
        : isPhoneNumber
            ? context.tr.phoneNumber
            : isBankAccount
                ? context.tr.bankAccount
                : '';

    final canShowField =
        isPhoneNumber || isBankAccount || phoneNumberOrBankAccount;

    return Consumer<PaymentVM>(
      builder: (context, paymentVM, child) {
        Future<void> addAndEditPayment() async {
          await paymentVM.addPayment(context,
              title: paymentValue.value!,
              description: descriptionController.text);
        }

        return BaseDialog(
            withCloseButton: true,
            isLoading: false,
            backgroundColor: context.appTheme.cardColor,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
              child: Form(
                key: formKey.value,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // * Header ===========================
                    Text(
                      context.tr.addPaymentMethod,
                      style: context.title,
                    ),
                    context.largeGap,

                    BaseDropDown(
                        hint: context.tr.addPaymentMethod,
                        onChanged: (value) {
                          paymentValue.value = value;
                        },
                        data: paymentVM.newList
                            .map(
                              (e) => DropdownMenuItem<String>(
                                value: e,
                                child: Text(
                                  e,
                                  style: context.subTitle,
                                ),
                              ),
                            )
                            .toList(),
                        value: paymentValue.value),

                    // * Field ===========================
                    if (canShowField) ...[
                      context.largeGap,
                      BaseTextField(
                        hint: isInstapay ? context.tr.phoneOrPayLink : hint,
                        // '${context.tr.phoneNumber} ${context.tr.Or} ${context.tr.bankAccount}',
                        controller: descriptionController,
                        textInputType: isInstapay
                            ? TextInputType.text
                            : TextInputType.number,
                      ),
                    ],

                    context.xLargeGap,

                    // * Add Product Button ========================
                    Button(
                      isLoading: paymentVM.isLoading,
                      isOutLine: paymentVM.isLoading,
                      label: context.tr.add,
                      onPressed: () async {
                        if (!formKey.value.currentState!.validate()) return;

                        if (paymentValue.value == null) {
                          context.showBarMessage(
                              context.tr.choosePaymentMethods,
                              isError: true);
                          return;
                        }

                        await addAndEditPayment();
                      },
                      isWhiteText: true,
                    ),
                  ],
                ),
              ),
            ));
      },
    );
  }
}
