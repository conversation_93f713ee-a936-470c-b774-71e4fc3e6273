import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/show_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/theme/theme.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../categories/models/category_model.dart';
import '../../../models/products_model.dart';
import '../../add_product/add_product.dart';

class EditAndDeleteButtons extends StatelessWidget {
  final ProductModel product;
  final CategoryModel cat;

  const EditAndDeleteButtons({
    super.key,
    required this.product,
    required this.cat,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: But<PERSON>(
            icon: CircleAvatar(
              backgroundColor: ColorManager.grey.withOpacity(0.5),
              child: const BaseLottieWidget.icon(
                Assets.animatedEdit,
                height: AppSpaces.iconSize,
              ),
            ),
            isPrefixIcon: true,
            onPressed: () {
              context.toReplacement(AddProductScreen(
                product: product,
                category: cat,
              ));
            },
            color: ColorManager.primaryColor,
            label: context.tr.edit,
            isWhiteText: true,
          ),
        ),
        context.mediumGap,
        Expanded(
          child: Button(
            icon: const CircleAvatar(
              backgroundColor: ColorManager.red,
              child: BaseLottieWidget.icon(
                Assets.animatedDelete,
                height: AppSpaces.iconSize,
              ),
            ),
            onPressed: () => showAlertDialog(context,
                child: Text(
                  context.tr.deleteProductConfirmationMessage,
                  style: context.labelLarge,
                ), onConfirm: () {
              context.read<ProductVM>().deleteProduct(
                    context,
                    id: product.documentId!,
                    cat: product.category!,
                    fromDetails: true,
                  );
            }),
            isPrefixIcon: true,
            isOutLine: true,
            color: Colors.red,
            label: context.tr.delete,
            isWhiteText: true,
          ),
        )
      ],
    );
  }
}
