import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';

import '../../../../../../../core/resources/app_spaces.dart';

class ChooseSizeCard extends StatelessWidget {
  final ExtraSettingsModel size;
  final bool isSelected;
  final bool withoutMark;

  const ChooseSizeCard(
      {super.key,
      required this.size,
      this.isSelected = false,
      this.withoutMark = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: AppSpaces.mediumPadding,
          vertical: AppSpaces.xSmallPadding),
      decoration: BoxDecoration(
          color: isSelected ? ColorManager.primaryColor : Colors.transparent,
          border: Border.fromBorderSide(
            BorderSide(color: isSelected ? Colors.transparent : Colors.grey),
          ),
          borderRadius: BorderRadius.all(Radius.circular(
              withoutMark ? AppRadius.baseRadius : AppRadius.smallRadius))),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Center(
              child: Text(
                size.nameByLang(context),
                textAlign: TextAlign.center,
                style: context.labelMedium.copyWith(
                    color: context.isDark || isSelected
                        ? ColorManager.white
                        : ColorManager.black,
                    fontWeight: FontWeight.w500),
              ),
            ),
            if (isSelected && !withoutMark) ...[
              context.smallGap,
              const Icon(
                Icons.check,
                size: 22,
                color: ColorManager.white,
              )
            ]
          ],
        ),
      ),
    );
  }
}
