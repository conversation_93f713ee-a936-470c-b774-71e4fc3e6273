import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/refresh_indictor/refresh_indictor_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/bulk_edit_screen/widgets/bulk_edit_products_list_view.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/store/view/widgets/store_tab_bar/store_tab_bar_widget.dart';
import 'package:idea2app_vendor_app/src/screens/store/view_model/stote_tab_bar_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/shared_widgets/animated/empty_data_widget.dart';
import '../../../../core/shared_widgets/appbar/main_appbar.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../categories/models/category_model.dart';
import '../../models/products_model.dart';

class BulkEditProductsScreen extends HookWidget {
  final CategoryModel category;

  const BulkEditProductsScreen({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    final productsVM = context.read<ProductVM>();
    final categoryVM = context.read<CategoryVM>();

    // * Search Field ========================
    final queryController = useTextEditingController();
    final isSearch = useState(false);

    // * Selected products for bulk edit ========================
    final selectedProducts = useState(<ProductModel>[]);
    final bulkEditData = useState(<String, Map<String, dynamic>>{});

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        categoryVM.getCategories(context);
        productsVM.getAllProducts(context);
      });

      return () {};
    }, []);

    return Consumer4<AuthVM, ProductVM, CategoryVM, StoreTabBarVM>(
      builder: (context, authVM, productsVM, categoryVM, storeTabBarVM, child) {
        final categoriesNameList = categoryVM.categories.map((e) {
          return context.isEng
              ? (e.englishName ?? e.arabicName ?? '')
              : (e.arabicName ?? e.englishName ?? '');
        }).toList();

        final tabsList = [context.tr.all, ...categoriesNameList];

        final colorIcon =
            context.isDark ? ColorManager.white : ColorManager.black;

        // Save function for bulk edit
        Future<void> saveBulkEdit() async {
          if (bulkEditData.value.isEmpty) {
            context.showBarMessage(context.tr.noChangesToSave, isError: true);
            return;
          }

          try {
            await productsVM.bulkUpdateProducts(context, bulkEditData.value);

            // Clear selections and data after successful save
            selectedProducts.value.clear();
            bulkEditData.value.clear();

            if (context.mounted) {
              context.showBarMessage(context.tr.bulkEditSavedSuccessfully);
            }
          } catch (e) {
            if (context.mounted) {
              context.showBarMessage(context.tr.somethingWentWrong,
                  isError: true);
            }
          }

          productsVM.getProductsByCategory(context,
              catId: category.documentId!);
        }

        return RefreshIndicatorWidget(
          onRefresh: () async {
            await Future.wait([
              categoryVM.getCategories(context),
              productsVM.getAllProducts(context),
            ]);
          },
          child: Scaffold(
            appBar: MainAppBar(
              onBackPressed: () {
                storeTabBarVM.setCurrentIndex(0);

                productsVM.getProductsByCategory(context,
                    catId: category.documentId!);

                context.back();
              },
              isCenterTitle: false,
              title: context.tr.bulkEdit,
              actionWidget: Button(
                isLoading: productsVM.isLoading,
                label: context.tr.save,
                onPressed: saveBulkEdit,
              ).sized(height: 40),
              haveBackButton: true,
            ),
            body: Column(
              children: [
                context.smallGap,
                BaseTextField(
                  controller: queryController,
                  onChanged: (value) {
                    if (value.isEmpty) {
                      isSearch.value = false;
                      productsVM.filterSearchedList(
                          query: '',
                          isAll: storeTabBarVM.currentIndex == 0,
                          context: context);
                      return;
                    }

                    isSearch.value = true;
                    queryController.text = value;
                    productsVM.filterSearchedList(
                        query: value,
                        isAll: storeTabBarVM.currentIndex == 0,
                        context: context);
                  },
                  suffixIcon: Icon(
                          isSearch.value ? Icons.close : CupertinoIcons.search,
                          color: colorIcon)
                      .onTap(() {
                    queryController.clear();
                    isSearch.value = false;
                    productsVM.filterSearchedList(
                        query: '', isAll: true, context: context);
                  }),
                  hint: context.tr.searchProduct,
                  withoutEnter: true,
                ).paddingSymmetric(horizontal: AppSpaces.mediumPadding),

                context.mediumGap,

                // Category tabs
                if (categoryVM.categories.isNotEmpty) ...[
                  StoreTapBarWidget(
                    tabs: tabsList,
                    onTab: (index) {
                      final isCurrentIndex =
                          storeTabBarVM.currentIndex == index;

                      if (isCurrentIndex) {
                        return;
                      }

                      storeTabBarVM.setCurrentIndex(index);

                      if (index == 0) {
                        if (productsVM.allProducts.isEmpty) {
                          productsVM.getAllProducts(context,
                              query: queryController.text);
                        }

                        if (queryController.text.isNotEmpty) {
                          productsVM.filterSearchedList(
                              query: queryController.text,
                              isAll: true,
                              context: context);
                        }
                      } else {
                        final selectedCategory =
                            categoryVM.categories[index - 1];
                        productsVM.getProductsByCategoryFromAllProducts(context,
                            catId: selectedCategory.documentId!,
                            query: queryController.text);
                      }
                    },
                  ),
                  context.mediumGap,
                ],

                // Products list
                Expanded(
                  child: Builder(builder: (context) {
                    final products = storeTabBarVM.currentIndex == 0
                        ? isSearch.value
                            ? productsVM.searchedProducts
                            : productsVM.allProducts
                        : productsVM.searchedProducts;

                    if (products.isEmpty || productsVM.isLoading) {
                      return EmptyDataWidget(
                        isLoading: productsVM.isLoading,
                        message: context.tr.noProductsFound,
                      );
                    } else {
                      return BulkEditProductsListView(
                        products: products,
                        selectedProducts: selectedProducts,
                        bulkEditData: bulkEditData,
                        category: category,
                      );
                    }
                  }),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
