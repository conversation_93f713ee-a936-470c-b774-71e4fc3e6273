import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/entrance_fader.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:provider/provider.dart';

import '../../../core/shared_widgets/loading/loading_widget.dart';
import '../view_model/settings_view_model.dart';

class DynamicSettingsScreen extends StatelessWidget {
  final String title;

  const DynamicSettingsScreen({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsVM>(
      builder: (context, settingsVM, child) {
        final isAboutUs = title == context.tr.aboutUs;
        final aboutUsText = context.isEng
            ? settingsVM.settings?.aboutUsEn
            : settingsVM.settings?.aboutUsAr;
        final termsAndConditionText = context.isEng
            ? settingsVM.settings?.termsEn
            : settingsVM.settings?.termsAr;

        final text = isAboutUs ? aboutUsText : termsAndConditionText;

        return Scaffold(
          appBar: MainAppBar(
            haveBackButton: true,
            title: context.tr.termsAndCondition,
          ),
          body: settingsVM.isLoading
              ? const LoadingWidget()
              : WidgetAnimator(
                  delay:
                      const Duration(milliseconds: AppConsts.animatedDuration),
                  child: Markdown(
                    data: text ?? '',
                    styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context))
                        .copyWith(
                      p: context.subTitle,
                      h1: context.title.copyWith(fontSize: 24),
                      h2: context.title.copyWith(fontSize: 22),
                      h3: context.title.copyWith(fontSize: 20),
                      h4: context.title.copyWith(fontSize: 18),
                      h5: context.title.copyWith(fontSize: 16),
                      h6: context.title.copyWith(fontSize: 14),
                    ),
                  ).paddingAll(AppSpaces.smallPadding),
                ),
        );
      },
    );
  }
}
