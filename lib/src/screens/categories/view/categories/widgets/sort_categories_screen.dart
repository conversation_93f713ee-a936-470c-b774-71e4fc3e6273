import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:provider/provider.dart';

class SortCategoriesScreen extends StatelessWidget {
  final List<CategoryModel> categories;
  const SortCategoriesScreen({super.key, required this.categories});

  @override
  Widget build(BuildContext context) {
    return Consumer<CategoryVM>(
      builder: (context, categoryVM, child) {
        return Scaffold(
          floatingActionButtonLocation: categoryVM.isLoading
              ? FloatingActionButtonLocation.miniCenterFloat
              : null,
          floatingActionButton: categoryVM.isLoading
              ? LoadingWidget()
              : FloatingActionButton(
                  backgroundColor: ColorManager.primaryColor,
                  onPressed: () async {
                    await Future.forEach(categories, (e) async {
                      await categoryVM.editCategory(
                          cat: e, context, getCategoriesAfterEdit: false);
                    });
                    await categoryVM.getCategories(context);

                    context.back();
                  },
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                  ),
                ),
          appBar: MainAppBar(
            title: context.tr.sortCategories,
            haveBackButton: true,
          ),
          body: ListView(
            padding: EdgeInsets.all(AppSpaces.mediumPadding),
            children: [
              Text(
                context.tr.sortCategoriesDescription,
                style: context.labelLarge,
              ),
              context.mediumGap,
              ReorderableListView(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  children: categories.indexed.map((e) {
                    final categoryName = context.isEng
                        ? (e.$2.englishName?.isNotEmpty == true
                            ? e.$2.englishName!
                            : e.$2.arabicName ?? '')
                        : (e.$2.arabicName?.isNotEmpty == true
                            ? e.$2.arabicName!
                            : e.$2.englishName ?? '');
                    return Container(
                      margin: EdgeInsets.only(bottom: AppSpaces.smallPadding),
                      key: ValueKey(e.$2),
                      decoration: BoxDecoration(
                        color: ColorManager.textFieldColor(context),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: ListTile(
                        leading: Text(
                          (e.$1 + 1).toString(),
                          style: context.labelMedium,
                        ),
                        title: Text(categoryName),
                        trailing: Icon(Icons.drag_handle),
                      ),
                    );
                  }).toList(),
                  onReorder: (oldIndex, newIndex) {
                    if (oldIndex < newIndex) {
                      newIndex -= 1;
                    }
                    final item = categories.removeAt(oldIndex);
                    categories.insert(newIndex, item);

                    //! Update sort numbers for all items
                    for (var i = 0; i < categories.length; i++) {
                      categories[i] = categories[i].copyWith(sortNumber: i + 1);
                    }

                    // Force rebuild
                    (context as Element).markNeedsBuild();

                    Log.f("Category Name ${categories.map((e) => e.toJson())}");
                  })
            ],
          ),
        );
      },
    );
  }
}
