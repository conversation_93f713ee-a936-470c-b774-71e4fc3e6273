import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/main_category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/sort_categories_only_screen.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/sort_main_categories_screen.dart';

class SortChoiceDialog extends StatelessWidget {
  final List<CategoryModel> categories;
  final List<MainCategoryModel> mainCategories;

  const SortChoiceDialog({
    super.key,
    required this.categories,
    required this.mainCategories,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSpaces.mediumPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Main Categories Button
            SizedBox(
              width: double.infinity,
              child: Button(
                label: context.tr.sortMainCategories,
                onPressed: () {
                  Navigator.pop(context);
                  context.to(SortMainCategoriesScreen(
                    mainCategories: mainCategories,
                  ));
                },
                color: ColorManager.primaryColor,
              ),
            ),

            context.mediumGap,

            // Categories Button
            SizedBox(
              width: double.infinity,
              child: Button(
                label: context.tr.sortCategories,
                onPressed: () {
                  Navigator.pop(context);
                  context.to(SortCategoriesOnlyScreen(
                    categories: categories,
                  ));
                },
                color: ColorManager.secondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
