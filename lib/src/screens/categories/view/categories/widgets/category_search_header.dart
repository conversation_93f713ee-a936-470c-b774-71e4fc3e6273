import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/widgets/sort_choice_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:provider/provider.dart';

import '../../../models/main_category_model.dart';
import '../../base_add_category_screen.dart';
import '../sort_categories_only_screen.dart';

class CategorySearchHeader extends StatelessWidget {
  final TextEditingController searchController;

  const CategorySearchHeader({
    super.key,
    required this.searchController,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<CategoryVM>(
      builder: (context, categoryVM, child) {
        final mainCategories = categoryVM.categories
            .expand(
              (cat) => cat.mainCategory != null
                  ? [cat.mainCategory!]
                  : <MainCategoryModel>[],
            )
            .toSet()
            .toList();

        return Padding(
          padding: EdgeInsets.only(
              right: AppSpaces.mediumPadding,
              left: AppSpaces.mediumPadding,
              top: AppSpaces.mediumPadding),
          child: Column(
            children: [
              SizedBox(
                height: 35.h,
                child: Row(
                  children: [
                    Expanded(
                      child: categoriesScreenButtonWidget(
                        context,
                        onPressed: () {
                          context.to(const BaseAddCategoryScreen());
                        },
                        text: context.tr.addCategory,
                        icon: CircleAvatar(
                          radius: 8.r,
                          backgroundColor: ColorManager.primaryColor,
                          child: Icon(
                            Icons.add,
                            color: ColorManager.white,
                            size: 14.r,
                          ),
                        ),
                      ),
                    ),
                    context.smallGap,
                    Expanded(
                      child: categoriesScreenButtonWidget(
                        context,
                        onPressed: () {
                          if (mainCategories.isEmpty) {
                            context.to(SortCategoriesOnlyScreen(
                              categories: categoryVM.categories,
                            ));
                          } else {
                            // mainCategoryVM.getMainCategories(context).then((_) {
                            //   final fetchedMainCategories =
                            //       mainCategoryVM.mainCategories;

                            // Show dialog to choose between main categories and categories
                            showDialog(
                                context: context,
                                builder: (context) => SortChoiceDialog(
                                      categories: categoryVM.categories,
                                      mainCategories: mainCategories,
                                    ));
                            //   ).then((_) {
                            //     // Go back when dialog is dismissed
                            //     Navigator.pop(context);
                            //   });
                            // });
                          }
                        },
                        text: context.tr.sortCategories,
                        icon: Icon(
                          CupertinoIcons.sort_down,
                          color: context.appTheme.primaryColorDark,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              context.mediumGap,
              BaseTextField(
                hint: context.tr.search,
                suffixIcon: searchController.text.isNotEmpty
                    ? Icon(Icons.close,
                            color: context.appTheme.primaryColorDark)
                        .onTap(
                        () {
                          searchController.clear();
                          categoryVM.getSearchedCategories('');
                        },
                      )
                    : null,
                withoutEnter: true,
                controller: searchController,
                onChanged: (value) {
                  categoryVM.getSearchedCategories(searchController.text);
                },
                icon: Icon(Icons.search,
                    color: context.appTheme.primaryColorDark),
              ),
            ],
          ),
        );
      },
    );
  }
}

Widget categoriesScreenButtonWidget(
  BuildContext context, {
  required VoidCallback onPressed,
  required String text,
  required Widget icon,
}) {
  return InkWell(
    onTap: onPressed,
    child: Container(
      height: 35.h,
      decoration: BoxDecoration(
        color: ColorManager.textFieldColor(context),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon,
          SizedBox(width: 5.w),
          Text(
            text,
            style: context.labelMedium.copyWith(
              color: context.appTheme.primaryColorDark,
            ),
          ),
        ],
      ),
    ),
  );
}
