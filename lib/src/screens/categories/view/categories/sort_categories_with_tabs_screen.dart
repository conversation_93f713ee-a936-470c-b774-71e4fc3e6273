// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
// import 'package:idea2app_vendor_app/src/screens/categories/view/categories/sort_categories_only_screen.dart';
// import 'package:idea2app_vendor_app/src/screens/categories/view/categories/widgets/sort_choice_dialog.dart';
// import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
// import 'package:idea2app_vendor_app/src/screens/categories/view_model/main_category_view_model.dart';
// import 'package:provider/provider.dart';
//
// class SortCategoriesWithTabsScreen extends HookWidget {
//   const SortCategoriesWithTabsScreen({
//     super.key,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Consumer2<CategoryVM, MainCategoryVM>(
//       builder: (context, categoryVM, mainCategoryVM, child) {
//         final categories = categoryVM.categories;
//
//         final mainCategories = categoryVM.categories
//             .expand(
//               (cat) => cat.mainCategory != null ? [cat.mainCategory!] : [],
//             )
//             .toSet()
//             .toList();
//
//         final hasMainCategories = mainCategories.isNotEmpty;
//
//         // Return a simple loading screen while navigation happens
//         return HookBuilder(builder: (context) {
//           // Check if main categories exist and show appropriate screen
//           useEffect(() {
//             WidgetsBinding.instance.addPostFrameCallback((_) {
//               if (hasMainCategories) {
//                 // Fetch main categories first
//                 mainCategoryVM.getMainCategories(context).then((_) {
//                   final fetchedMainCategories = mainCategoryVM.mainCategories;
//
//                   // Show dialog to choose between main categories and categories
//                   showDialog(
//                     context: context,
//                     builder: (context) => SortChoiceDialog(
//                       categories: categories,
//                       mainCategories: fetchedMainCategories,
//                     ),
//                   ).then((_) {
//                     // Go back when dialog is dismissed
//                     Navigator.pop(context);
//                   });
//                 });
//               } else {
//                 // No main categories, go directly to categories sorting
//                 context.toReplacement(SortCategoriesOnlyScreen(
//                   categories: categories,
//                 ));
//               }
//             });
//             return () {};
//           }, []);
//
//           return Scaffold(
//             body: Center(
//               child: CircularProgressIndicator(),
//             ),
//           );
//         });
//       },
//     );
//   }
// }
