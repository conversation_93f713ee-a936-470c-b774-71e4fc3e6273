import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/shared_widgets/appbar/main_appbar.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';

class SortCategoriesOnlyScreen extends HookWidget {
  final List<CategoryModel> categories;

  const SortCategoriesOnlyScreen({
    super.key,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<CategoryVM>(
      builder: (context, categoryVM, child) {
        return HookBuilder(builder: (context) {
          final sortedCategories =
              useState(List<CategoryModel>.from(categories));

          void onCategoryReorder(int oldIndex, int newIndex) {
            if (oldIndex < newIndex) {
              newIndex -= 1;
            }
            final item = sortedCategories.value.removeAt(oldIndex);
            sortedCategories.value.insert(newIndex, item);

            // Update sort numbers
            for (var i = 0; i < sortedCategories.value.length; i++) {
              sortedCategories.value[i] =
                  sortedCategories.value[i].copyWith(sortNumber: i + 1);
            }
            sortedCategories.value = List.from(sortedCategories.value);
          }

          Future<void> saveChanges() async {
            try {
              // Prepare categories data
              final Map<String, Map<String, dynamic>> categoriesData = {};

              for (var category in sortedCategories.value) {
                if (category.documentId != null) {
                  categoriesData[category.documentId!] = {
                    'sort': category.sortNumber,
                  };
                }
              }

              // Call bulk update API for categories
              await categoryVM.bulkUpdateCategories(
                context,
                categoriesData: categoriesData,
              );

              // Refresh data
              await categoryVM.getCategories(context);

              if (context.mounted) {
                Navigator.pop(context);
              }
            } catch (e) {
              if (context.mounted) {
                context.showBarMessage(context.tr.somethingWentWrong,
                    isError: true);
              }
            }
          }

          return Scaffold(
            appBar: MainAppBar(
              title: context.tr.sortCategories,
              haveBackButton: true,
              isCenterTitle: false,
              actionWidget: Button(
                isLoading: categoryVM.isLoading,
                label: context.tr.save,
                onPressed: saveChanges,
              ).sized(
                height: 40,
              ),
            ),
            body: Column(
              children: [
                // Description
                Padding(
                  padding: EdgeInsets.all(AppSpaces.mediumPadding),
                  child: Text(
                    context.tr.sortCategoriesDescription,
                    style: context.labelMedium,
                    textAlign: TextAlign.center,
                  ),
                ),

                // Categories list
                Expanded(
                  child: _buildCategoriesList(
                    sortedCategories.value,
                    onCategoryReorder,
                    context,
                  ),
                ),
              ],
            ),
          );
        });
      },
    );
  }

  Widget _buildCategoriesList(
    List<CategoryModel> categories,
    Function(int, int) onReorder,
    BuildContext context,
  ) {
    return ReorderableListView(
      padding: EdgeInsets.all(AppSpaces.mediumPadding),
      onReorder: onReorder,
      children: categories.indexed
          .map((e) => ListTile(
                key: ValueKey(e.$2.documentId),
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: context.appTheme.primaryColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      (e.$1 + 1).toString(),
                      style: context.labelMedium.copyWith(
                        color: context.appTheme.primaryColor,
                      ),
                    ),
                  ),
                ),
                title: Text(
                  e.$2.nameByLang(context),
                  style: context.labelMedium,
                ),
                trailing: Icon(
                  Icons.drag_handle,
                  color: context.appTheme.primaryColor,
                ),
              ))
          .toList(),
    );
  }
}
