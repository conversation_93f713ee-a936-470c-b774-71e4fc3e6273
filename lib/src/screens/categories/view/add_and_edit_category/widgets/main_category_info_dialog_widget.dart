import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/icon_widget/icon_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';

class MainCategoryInfoDialogWidget extends HookWidget {
  const MainCategoryInfoDialogWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BaseDialog(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.imageContainerRadius),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                IconWidget(
                    icon: Assets.categoryNav,
                    height: 30,
                    width: 30,
                    color: context.appTheme.primaryColorDark),
                Text(
                  context.tr.mainCategory,
                  style: context.subHeadLine,
                ),
              ],
            ),
            context.mediumGap,
            Text(
              context.tr.mainCategorySettingsDesc,
              style: context.labelLarge.copyWith(),
              textAlign: TextAlign.start,
            ),
            Image.asset(
              Assets.mainCategoryGif,
              width: double.infinity,
            ),
            Button(
              label: context.tr.confirm,
              onPressed: () => context.back(),
            )
          ],
        ),
      ),
    );
  }
}
