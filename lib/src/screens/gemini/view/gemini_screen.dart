import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/models/select_ai_item.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/view/sections/chat.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/view/sections/chat_stream.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/view/sections/embed_batch_contents.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/view/sections/embed_content.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/view/sections/response_widget_stream.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/view/sections/stream.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/view/sections/text_and_image.dart';
import 'package:idea2app_vendor_app/src/screens/gemini/view/sections/text_only.dart';

class GeminiScreen extends StatefulWidget {
  const GeminiScreen({super.key});

  @override
  State<GeminiScreen> createState() => _GeminiScreenState();
}

class _GeminiScreenState extends State<GeminiScreen> {
  final int _selectedItem = 0;

  final _sections = <SectionItemModel>[
    SectionItemModel(0, 'Stream text', const SectionTextStreamInput()),
    SectionItemModel(1, 'textAndImage', const SectionTextAndImageInput()),
    SectionItemModel(2, 'chat', const SectionChat()),
    SectionItemModel(3, 'Stream chat', const SectionStreamChat()),
    SectionItemModel(4, 'text', const SectionTextInput()),
    SectionItemModel(5, 'embedContent', const SectionEmbedContent()),
    SectionItemModel(
        6, 'batchEmbedContents', const SectionBatchEmbedContents()),
    SectionItemModel(
        7, 'response without setState()', const ResponseWidgetSection()),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(
        haveBackButton: true,
        title: 'Idea2App AI',
        // actionWidget: PopupMenuButton<int>(
        //   initialValue: _selectedItem,
        //   onSelected: (value) => setState(() => _selectedItem = value),
        //   itemBuilder: (context) => _sections.map((e) {
        //     return PopupMenuItem<int>(value: e.index, child: Text(e.title));
        //   }).toList(),
        //   child: const Icon(
        //     Icons.more_vert_rounded,
        //     color: Colors.white,
        //   ),
        // )
      ),
      body: IndexedStack(
        index: _selectedItem,
        children: _sections.map((e) => e.widget).toList(),
      ),
    );
  }
}
