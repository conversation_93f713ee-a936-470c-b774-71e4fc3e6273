import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/data/local/shared_preferences/get_storage.dart';

import '../../../../core/data/local/local_keys.dart';

class SettingsLocalRepo {
  Locale currentLocal() {
    final langCode = GetStorageHandler.getLocalData(key: LocalKeys.language);

    if (langCode != null) {
      return Locale(langCode);
    } else {
      if (kIsWeb) {
        return const Locale('en');
      }

      String languageCode = Platform.localeName.split('_')[0];

      return Locale(languageCode);
    }
  }

  Future<void> updateLanguage(Locale locale) async {
    GetStorageHandler.setLocalData(
        key: LocalKeys.language, value: locale.languageCode);
  }

  ThemeMode themeMode() {
    final index = GetStorageHandler.getLocalData(key: LocalKeys.theme);

    if (index != null) {
      return ThemeMode.values[index];
    } else {
      return ThemeMode.system;
    }
  }

  Future<void> updateThemeMode(ThemeMode theme) async {
    GetStorageHandler.setLocalData(key: LocalKeys.theme, value: theme.index);
  }
}
