import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';

import '../../../../core/resources/app_radius.dart';
import '../../../../core/resources/app_spaces.dart';
import '../../../../core/resources/theme/theme.dart';
import '../../../../core/shared_widgets/icon_widget/icon_widget.dart';
import '../../../dashboard/models/extra_setting_model.dart';
import '../../../products/view/add_product/widgets/fields/colors/choose_color_card.dart';

class InvoicesProductCardWidget extends StatelessWidget {
  final ProductQuantityModel productQuantity;

  const InvoicesProductCardWidget({super.key, required this.productQuantity});

  @override
  Widget build(BuildContext context) {
    final product = productQuantity.product;
    final isColorFound = productQuantity.color.isNotEmpty;
    final isSizeFound = productQuantity.size.isNotEmpty;
    final productPrice = productQuantity.price ?? product?.actualPrice ?? 0;

    final totalPrice = productPrice * (productQuantity.quantity ?? 1);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(AppSpaces.smallPadding),
            decoration: BoxDecoration(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              color: context.appTheme.cardColor,
            ),
            child: Row(
              children: [
                //! product image
                ClipRRect(
                  borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                  child: Image.network(
                    product?.thumbnail?.url ?? '',
                    fit: BoxFit.cover,
                    height: 70.h,
                    width: 70.w,
                    errorBuilder: (context, error, stackTrace) {
                      return const IconWidget(icon: Assets.imagesAppLogo);
                    },
                  ),
                ).paddingAll(AppSpaces.smallPadding),

                context.smallGap,

                //! product name & price
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //! Product name
                      Text(
                        product?.englishTitle ?? '',
                        style: context.labelLarge,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        productPrice.toCurrency(context),
                        style: context.labelLarge,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      //! Size & Color
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          if (isSizeFound) ...[
                            Text(
                              '${context.tr.size}: ${productQuantity.size}',
                              style: context.labelLarge.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            context.smallGap,
                          ],
                          if (isColorFound) ...[
                            Text(
                              '${context.tr.color}:',
                              style: context.labelLarge.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            context.smallGap,
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: ChooseColorCard(
                                  color: ExtraSettingsModel(
                                      englishName: productQuantity.color)),
                            )
                          ]
                        ],
                      ),

                      //! Total Price
                      Row(
                        children: [
                          Text(
                            context.tr.totalPrice,
                            style: context.labelLarge,
                          ),
                          context.xSmallGap,
                          Text(
                            totalPrice.toCurrency(context),
                            style: context.labelLarge
                                .copyWith(color: ColorManager.primaryColor),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        context.smallGap,
        Text(' x ${productQuantity.quantity}',
            style: context.labelLarge.copyWith(
                color: ColorManager.primaryColor, fontWeight: FontWeight.bold)),
      ],
    );
  }
}
