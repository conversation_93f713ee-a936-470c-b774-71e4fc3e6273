import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/repository/invoices_repos/store_order_invoices.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;

baseText(
  String text, {
  required Font arabicFont,
  bool isBold = false,
  bool isLtr = false,
  PdfColor? color,
  double fontSize = 20,
}) {
  var english = RegExp(r'[a-zA-Z]');

  var isEnglishText = english.hasMatch(text);

  return pw.Padding(
    padding: const pw.EdgeInsets.all(4),
    child: pw.Text(
      text,
      textScaleFactor: 1,
      textAlign: isLtr ? pw.TextAlign.left : pw.TextAlign.right,
      textDirection:
          isLtr || isEnglishText ? pw.TextDirection.ltr : pw.TextDirection.rtl,
      style: pw.TextStyle(
        font: arabicFont,
        fontSize: fontSize,
        fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
        color: color,
      ),
    ),
  );
}

arabicBaseText(
  String text, {
  bool isBold = false,
  bool isLtr = false,
  PdfColor? color,
  double fontSize = 20,
}) {
  return pw.Padding(
    padding: const pw.EdgeInsets.all(4),
    child: pw.Text(
      text,
      textScaleFactor: 1,
      textDirection: pw.TextDirection.rtl,
      style: pw.TextStyle(
        fontSize: fontSize,
        fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
        color: color,
      ),
    ),
  );
}

pw.Column englishCustomerInfo(
  BuildContext appContext, {
  required Font arabicFont,
  required OrderModel order,
}) {
  return pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
    //! Customer Information --------------------------------

    arabicBaseText(appContext.tr.customerInfo,
        fontSize: 24, isBold: true, isLtr: true),

    // ),

    pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
      pw.Expanded(
        child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
          //! Customer Name --------------------------------
          baseTableText('${appContext.tr.name}:',
              arabicFont: arabicFont, fontSize: 20, isBold: true),
          baseTableText(order.user?.displayName ?? '',
              fontSize: 20, arabicFont: arabicFont, isBold: true),
        ]),
      ),
      pw.Expanded(
        child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
          //! Customer Name --------------------------------
          baseTableText('${appContext.tr.phone}:',
              arabicFont: arabicFont, fontSize: 20, isBold: true),
          baseTableText(order.user?.phone ?? order.guestPhoneNumber ?? '',
              fontSize: 20, arabicFont: arabicFont, isBold: true),
        ]),
      )
    ]),

    //! Customer Address --------------------------------

    pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
      pw.Expanded(
        child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
          //! Customer Name --------------------------------
          baseTableText('${appContext.tr.city}:',
              arabicFont: arabicFont, fontSize: 20, isBold: true),
          baseTableText(order.addressModel?.city ?? '',
              fontSize: 20, arabicFont: arabicFont, isBold: true),
        ]),
      ),
      if (order.addressModel?.area != null && order.addressModel?.area != '-')
        pw.Expanded(
          child:
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
            //! Customer Name --------------------------------
            baseTableText('${appContext.tr.area}:',
                arabicFont: arabicFont, fontSize: 20, isBold: true),
            baseTableText(order.addressModel?.area ?? '-',
                fontSize: 20, arabicFont: arabicFont, isBold: true),
          ]),
        ),
    ]),

    pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
      //! Customer Name --------------------------------
      baseTableText('${appContext.tr.streetName}:',
          arabicFont: arabicFont, fontSize: 20, isBold: true),

      baseTableText(order.addressModel?.streetName ?? '',
          fontSize: 20, arabicFont: arabicFont, isBold: true),
    ]),

    pw.Row(children: [
      if (order.addressModel?.building != null &&
          order.addressModel?.building != '')
        pw.Expanded(
          child: baseText(
              '${appContext.tr.building}: ${order.addressModel?.building}',
              arabicFont: arabicFont,
              isLtr: true),
        ),
      if (order.addressModel?.floor != null && order.addressModel?.floor != '')
        pw.Expanded(
          child: baseText(
              '${appContext.tr.floor}: ${order.addressModel?.floor}',
              arabicFont: arabicFont,
              isLtr: true),
        ),
      if (order.addressModel?.apartment != null &&
          order.addressModel?.apartment != '')
        pw.Expanded(
          child: baseText(
              '${appContext.tr.apartment}: ${order.addressModel?.apartment}',
              arabicFont: arabicFont,
              isLtr: true),
        ),
    ]),
  ]);
}

pw.Column arabicCustomerInfo(
  BuildContext appContext, {
  required Font arabicFont,
  required OrderModel order,
}) {
  return pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.end, children: [
    //! Customer Information --------------------------------

    baseTableText(appContext.tr.customerInfo,
        arabicFont: arabicFont, fontSize: 24, isBold: true),

    // ),

    //! Customer Name & Phone --------------------------------
    pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        mainAxisAlignment: pw.MainAxisAlignment.end,
        children: [
          pw.Expanded(
              child: pw.Column(children: [
            pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
              baseTableText(order.user?.phone ?? '',
                  fontSize: 20, arabicFont: arabicFont, isBold: true),
              //! Customer Name --------------------------------
              baseTableText('${appContext.tr.phone}:',
                  arabicFont: arabicFont, fontSize: 20, isBold: true),
            ]),
            if (order.isGuestPhoneExistAndIsNotSameAsUserPhone)
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                baseTableText(
                  order.guestPhoneNumber ?? '',
                  fontSize: 20,
                  arabicFont: arabicFont,
                  isBold: true,
                ),
                //! Customer Name --------------------------------
                baseTableText('${appContext.tr.phone2}:',
                    arabicFont: arabicFont, fontSize: 20, isBold: true),
              ]),
          ])),
          pw.Expanded(
            child:
                pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
              baseTableText(order.user?.displayName ?? '',
                  fontSize: 20, arabicFont: arabicFont, isBold: true),
              //! Customer Name --------------------------------
              baseTableText('${appContext.tr.name}:',
                  arabicFont: arabicFont, fontSize: 20, isBold: true),
            ]),
          )
        ]),

    //! Customer City & Area --------------------------------

    pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
      pw.Expanded(
        child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
          baseTableText(order.addressModel?.city ?? '',
              fontSize: 20, arabicFont: arabicFont, isBold: true),
          //! Customer Name --------------------------------
          baseTableText('${appContext.tr.city}:',
              arabicFont: arabicFont, fontSize: 20, isBold: true),
        ]),
      ),
      if (order.addressModel?.area != null && order.addressModel?.area != '-')
        pw.Expanded(
          child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            baseTableText(order.addressModel?.area ?? '-',
                fontSize: 20, arabicFont: arabicFont, isBold: true),
            //! Customer Name --------------------------------
            baseTableText('${appContext.tr.area}:',
                arabicFont: arabicFont, fontSize: 20, isBold: true),
          ]),
        ),
    ]),
    pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
      baseTableText(order.addressModel?.streetName ?? '',
          fontSize: 20, arabicFont: arabicFont, isBold: true),
      //! Customer Name --------------------------------
      baseTableText('${appContext.tr.streetName}:',
          arabicFont: arabicFont, fontSize: 20, isBold: true),
    ]),

    //! Customer Building & Flor & Apartment --------------------------------

    pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
      if (order.addressModel?.apartment != null &&
          order.addressModel?.apartment != '')
        pw.Expanded(
          child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            baseTableText(order.addressModel?.apartment ?? '',
                fontSize: 20, arabicFont: arabicFont, isBold: true),
            //! Customer Name --------------------------------
            baseTableText('${appContext.tr.apartment}:',
                arabicFont: arabicFont, fontSize: 20, isBold: true),
          ]),
        ),
      if (order.addressModel?.floor != null && order.addressModel?.floor != '')
        pw.Expanded(
          child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            baseTableText(order.addressModel?.floor ?? '',
                fontSize: 20, arabicFont: arabicFont, isBold: true),
            //! Customer Name --------------------------------
            baseTableText('${appContext.tr.floor}:',
                arabicFont: arabicFont, fontSize: 20, isBold: true),
          ]),
        ),
      if (order.addressModel?.building != null &&
          order.addressModel?.building != '')
        pw.Expanded(
          child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            baseTableText(order.addressModel?.building ?? '',
                fontSize: 20, arabicFont: arabicFont, isBold: true),
            //! Customer Name --------------------------------
            baseTableText('${appContext.tr.building}:',
                arabicFont: arabicFont, fontSize: 20, isBold: true),
          ]),
        ),
    ]),
  ]);
}
