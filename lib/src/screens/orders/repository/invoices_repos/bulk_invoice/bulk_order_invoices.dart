import 'package:colornames/colornames.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/repository/invoices_repos/online_invoice/widgets/customer_info_section.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

import '../store_order_invoices.dart';

Future<pw.Document> generateBulkOrderInvoicesPDF(BuildContext appContext,
    {required List<OrderModel> orders, required bool isEnglish}) async {
  final currentVendor = appContext.read<AuthVM>().currentVendor;

  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  // Generate a page for each order
  for (int orderIndex = 0; orderIndex < orders.length; orderIndex++) {
    final order = orders[orderIndex];

    // Helper functions to check if colors and sizes exist in the order
    bool hasColors =
        order.products?.any((product) => product.color.isNotEmpty) ?? false;
    bool hasSizes =
        order.products?.any((product) => product.size.isNotEmpty) ?? false;

    englishTable() {
      // Build header row dynamically based on available data
      List<pw.Widget> headerCells = [
        baseTableText(
          'Product',
          arabicFont: arabicFont,
          isBold: true,
        ),
      ];

      if (hasSizes) {
        headerCells.add(baseTableText(
          'Size',
          arabicFont: arabicFont,
          isBold: true,
        ));
      }

      if (hasColors) {
        headerCells.add(baseTableText(
          'Color',
          arabicFont: arabicFont,
          isBold: true,
        ));
      }

      headerCells.addAll([
        baseTableText(
          'Quantity',
          arabicFont: arabicFont,
          isBold: true,
        ),
        baseTableText(
          'Price',
          arabicFont: arabicFont,
          isBold: true,
        ),
        baseTableText(
          'Total',
          arabicFont: arabicFont,
          isBold: true,
        ),
      ]);

      List<pw.TableRow> children = [
        pw.TableRow(children: headerCells),
      ];

      // Add product details to the table
      for (var productQuantity in order.products!) {
        final productTotal =
            productQuantity.actualPrice * productQuantity.quantity!;

        // Build row cells dynamically based on available data
        List<pw.Widget> rowCells = [
          baseTableText(
            productQuantity.nameByLang(appContext),
            arabicFont: arabicFont,
          ),
        ];

        if (hasSizes) {
          rowCells.add(baseTableText(
            productQuantity.size.isEmpty ? '-' : productQuantity.size,
            arabicFont: arabicFont,
          ));
        }

        if (hasColors) {
          rowCells.add(baseTableText(
            productQuantity.color.isEmpty
                ? '-'
                : ColorNames.guess(Color(productQuantity.color.toInt())),
            arabicFont: arabicFont,
          ));
        }

        rowCells.addAll([
          baseTableText(
            productQuantity.quantity.toString(),
            arabicFont: arabicFont,
          ),
          baseTableText(
            toInvoiceCurrency(appContext, price: productQuantity.actualPrice),
            arabicFont: arabicFont,
          ),
          baseTableText(
            toInvoiceCurrency(appContext, price: productTotal),
            arabicFont: arabicFont,
          ),
        ]);

        children.add(pw.TableRow(children: rowCells));
      }

      // Calculate column widths dynamically
      Map<int, pw.TableColumnWidth> columnWidths = {};
      int columnIndex = 0;

      // Product column
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);

      // Size column (if exists)
      if (hasSizes) {
        columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
      }

      // Color column (if exists)
      if (hasColors) {
        columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
      }

      // Quantity, Price, Total columns
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);

      return pw.Table(
        columnWidths: columnWidths,
        border: pw.TableBorder.all(),
        children: children,
      );
    }

    arabicTable() {
      //? reverse the order of the columns for Arabic
      // Build header row dynamically based on available data (reversed for Arabic)
      List<pw.Widget> headerCells = [
        baseTableText(
          'Total',
          arabicFont: arabicFont,
          isBold: true,
        ),
        baseTableText(
          'Price',
          arabicFont: arabicFont,
          isBold: true,
        ),
        baseTableText(
          'Quantity',
          arabicFont: arabicFont,
          isBold: true,
        ),
      ];

      if (hasColors) {
        headerCells.add(baseTableText(
          'Color',
          arabicFont: arabicFont,
          isBold: true,
        ));
      }

      if (hasSizes) {
        headerCells.add(baseTableText(
          'Size',
          arabicFont: arabicFont,
          isBold: true,
        ));
      }

      headerCells.add(baseTableText(
        'Product',
        arabicFont: arabicFont,
        isBold: true,
      ));

      List<pw.TableRow> children = [
        pw.TableRow(children: headerCells),
      ];

      // Add product details to the table
      for (var product in order.products!) {
        final productTotal = product.actualPrice * product.quantity!;

        // Build row cells dynamically based on available data (reversed for Arabic)
        List<pw.Widget> rowCells = [
          baseTableText(
            toInvoiceCurrency(appContext, price: productTotal),
            arabicFont: arabicFont,
          ),
          baseTableText(
            toInvoiceCurrency(appContext, price: product.actualPrice),
            arabicFont: arabicFont,
          ),
          baseTableText(
            product.quantity.toString(),
            arabicFont: arabicFont,
          ),
        ];

        if (hasColors) {
          rowCells.add(baseTableText(
            product.color.isEmpty
                ? '-'
                : ColorNames.guess(Color(product.color.toInt())),
            arabicFont: arabicFont,
          ));
        }

        if (hasSizes) {
          rowCells.add(baseTableText(
            product.size.isEmpty ? '-' : product.size,
            arabicFont: arabicFont,
          ));
        }

        rowCells.add(baseTableText(
          product.nameByLang(appContext),
          arabicFont: arabicFont,
        ));

        children.add(pw.TableRow(children: rowCells));
      }

      // Calculate column widths dynamically (reversed for Arabic)
      Map<int, pw.TableColumnWidth> columnWidths = {};
      int columnIndex = 0;

      // Total, Price, Quantity columns
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);

      // Color column (if exists)
      if (hasColors) {
        columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
      }

      // Size column (if exists)
      if (hasSizes) {
        columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
      }

      // Product column
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);

      return pw.Table(
        columnWidths: columnWidths,
        border: pw.TableBorder.all(),
        children: children,
      );
    }

    final vendorImage = await networkImage(currentVendor?.logo?.url ?? '');

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return <pw.Widget>[
            pw.Header(
                level: 0,
                child: pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 10),
                    child: pw.Align(
                        alignment: isEnglish
                            ? pw.Alignment.centerLeft
                            : pw.Alignment.centerRight,
                        child: pw.Column(
                          crossAxisAlignment: isEnglish
                              ? pw.CrossAxisAlignment.start
                              : pw.CrossAxisAlignment.end,
                          children: [
                            pw.Row(
                                mainAxisAlignment:
                                    pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  //! Order Id --------------------------------
                                  baseTableText(
                                    'Order ID: ${isEnglish ? '#${order.orderId}' : '${order.orderId}#'}',
                                    arabicFont: arabicFont,
                                    fontSize: 16,
                                  ),
                                  //! Order Id --------------------------------
                                  baseTableText(
                                    'Order ${orderIndex + 1} of ${orders.length}',
                                    arabicFont: arabicFont,
                                    fontSize: 14,
                                    isBold: true,
                                  ),
                                ]),
                            pw.SizedBox(height: 10),
                            pw.Row(
                                mainAxisAlignment:
                                    pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  pw.Column(
                                      crossAxisAlignment: isEnglish
                                          ? pw.CrossAxisAlignment.start
                                          : pw.CrossAxisAlignment.end,
                                      children: [
                                        baseTableText(
                                          currentVendor?.businessName ?? '',
                                          arabicFont: arabicFont,
                                          fontSize: 18,
                                          isBold: true,
                                        ),
                                        baseTableText(
                                          currentVendor?.phone ?? '',
                                          arabicFont: arabicFont,
                                          fontSize: 14,
                                        ),
                                        baseTableText(
                                          currentVendor?.address ?? '',
                                          arabicFont: arabicFont,
                                          fontSize: 14,
                                        ),
                                      ]),
                                  if (vendorImage != null)
                                    pw.Image(vendorImage,
                                        width: 80, height: 80),
                                ]),
                          ],
                        )))),
            pw.SizedBox(height: 20),
            if (isEnglish)
              englishCustomerInfo(appContext,
                  arabicFont: arabicFont, order: order)
            else
              arabicCustomerInfo(appContext,
                  arabicFont: arabicFont, order: order),
            pw.SizedBox(height: 20),
            isEnglish ? englishTable() : arabicTable(),
            pw.SizedBox(height: 20),
            if (order.isFromStore == false)
              pw.Padding(
                padding: const pw.EdgeInsets.only(top: 10),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    //! Sub Total Price
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        baseTableText(
                          toInvoiceCurrency(appContext,
                              price: order.total! - order.deliveryCost!),
                          arabicFont: arabicFont,
                          fontSize: 18,
                          isBold: true,
                        ),
                        baseTableText(
                          'Sub Total:',
                          arabicFont: arabicFont,
                          isBold: true,
                          fontSize: 18,
                        ),
                      ],
                    ), //! Total Price
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        baseTableText(
                          toInvoiceCurrency(appContext,
                              price: order.deliveryCost),
                          arabicFont: arabicFont,
                          fontSize: 18,
                          isBold: true,
                        ),
                        baseTableText(
                          'Delivery Fee:',
                          arabicFont: arabicFont,
                          isBold: true,
                          fontSize: 18,
                        ),
                      ],
                    ), //! Total Price
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        baseTableText(
                          toInvoiceCurrency(appContext, price: order.total),
                          arabicFont: arabicFont,
                          fontSize: 18,
                          isBold: true,
                        ),
                        baseTableText(
                          'Total Price:',
                          arabicFont: arabicFont,
                          isBold: true,
                          fontSize: 18,
                        ),
                      ],
                    ),
                  ],
                ),
              )
            else
              pw.Padding(
                padding: const pw.EdgeInsets.only(top: 10),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    //! Total Price
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        baseTableText(
                          toInvoiceCurrency(appContext, price: order.total),
                          arabicFont: arabicFont,
                          fontSize: 18,
                          isBold: true,
                        ),
                        baseTableText(
                          'Total Price:',
                          arabicFont: arabicFont,
                          isBold: true,
                          fontSize: 18,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ];
        },
      ),
    );
  }

  return pdf;
}
