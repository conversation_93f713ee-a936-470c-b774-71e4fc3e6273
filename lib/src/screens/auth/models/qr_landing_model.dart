enum QrType { social, menu }

extension QrTypeStringExtension on String {
  QrType get qrType {
    switch (this) {
      case 'social':
        return QrType.social;
      case 'menu':
        return QrType.menu;
      default:
        return QrType.social;
    }
  }
}

extension QrTypeExtension on QrType {
  String get toQrTypeString {
    switch (this) {
      case QrType.social:
        return 'social';
      case QrType.menu:
        return 'menu';
    }
  }
}

class QrLandingModel {
  final String? title;
  final String? description;
  final String? link;
  final QrType type;

  const QrLandingModel({
    this.title,
    this.description,
    this.link,
    required this.type,
  });

  factory QrLandingModel.fromJson(Map<String, dynamic> json) {
    return QrLandingModel(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      link: json['link'],
      type: (json['type'] as String).qrType,
    );
  }

  Map<String, dynamic> toJson() => {
        if (title != null) 'title': title,
        if (description != null) 'description': description,
        'type': type.toQrTypeString,
      };
}
