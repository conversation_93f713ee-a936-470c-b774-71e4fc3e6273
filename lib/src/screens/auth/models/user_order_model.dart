import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/user_model.dart';

class UserOrderModel {
  final String guestName;
  final String phoneNumber;
  final String deviceToken;
  final UserModel user;
  final int totalOrders;
  final int canceledOrders;
  final int refundedOrders;

  UserOrderModel({
    required this.guestName,
    required this.phoneNumber,
    required this.deviceToken,
    required this.user,
    required this.totalOrders,
    required this.canceledOrders,
    required this.refundedOrders,
  });

  factory UserOrderModel.fromJson(Map<String, dynamic> json) {
    final user = json[ApiStrings.user] != null
        ? UserModel.fromJson(json[ApiStrings.user])
        : const UserModel();

    return UserOrderModel(
      guestName: json[ApiStrings.guestName] ?? '',
      phoneNumber: json[ApiStrings.phoneNumber] ?? '',
      deviceToken: json[ApiStrings.deviceToken] ?? '',
      user: user,
      totalOrders: json[ApiStrings.totalOrders] ?? 0,
      canceledOrders: json[ApiStrings.canceledOrders] ?? 0,
      refundedOrders: json[ApiStrings.refundedOrders] ?? 0,
    );
  }
}
