import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/home_navigations/bottom_nav_bar_widget.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/alert_screen.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/dashboard_screen.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/home_page.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/bottom_nav_provider.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';

import '../../../core/resources/theme/theme.dart';
import '../../categories/view/categories/home_category_screen.dart';
import '../../drawer/app_drawer.dart';
import '../../orders/view/orders_screen.dart';
import '../../orders/view_model/order_view_model.dart';

class MainScreen extends HookWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.watch<AuthVM>();
    final orderVM = context.watch<OrderVM>();

    if (authVM.isExpiredFromOneWeek(context)) {
      return const AlertSubscriptionWidget();
    }

    // Get app lifecycle state
    final appLifecycleState = useAppLifecycleState();

    // App lifecycle effect - refresh data when app resumes from background
    useEffect(() {
      if (appLifecycleState == AppLifecycleState.resumed) {
        Log.i('App resumed - refreshing order data');
        // Schedule the calls after the current build cycle completes
        WidgetsBinding.instance.addPostFrameCallback((_) {
          orderVM.getHomeOrders(context);
          orderVM.getOrderStatistics(context);
        });
      }
      return () {};
    }, [appLifecycleState]);

    if (!authVM.isActive) {
      return const AlertSubscriptionWidget();
    }

    return Selector<BottomNavbarVM, int>(
      selector: (context, provider) => provider.currentIndex,
      builder: (context, currentIndex, child) {
        return WillPopScope(
          onWillPop: () {
            QuickAlert.show(
                type: QuickAlertType.error,
                title: context.tr.exitTheApp,
                text: context.tr.areYouSureYouWantToExitTheApp,
                context: context,
                cancelBtnText: context.tr.cancel,
                showCancelBtn: true,
                onCancelBtnTap: () {
                  context.back();
                },
                confirmBtnText: context.tr.confirm,
                confirmBtnColor: ColorManager.errorColor,
                onConfirmBtnTap: () {
                  exit(0);
                });

            return Future.value(false);
          },
          child: Scaffold(
            body: _SelectedScreen(
              currentIndex: currentIndex,
            ),
            bottomNavigationBar: const BottomNavBarWidget(),
            drawer: const AppDrawer(),
            floatingActionButton:
                (currentIndex == 2 && orderVM.hasSelectedOrders)
                    ? FloatingActionButton(
                        onPressed: () async {
                          await orderVM.generateBulkOrderInvoices(context);
                          orderVM.clearSelection();
                        },
                        backgroundColor: ColorManager.primaryColor,
                        child: const Icon(
                          Icons.print,
                          color: ColorManager.white,
                        ),
                      )
                    : null,
            // floatingActionButtonLocation:
            //     FloatingActionButtonLocation.centerDocked,
            // floatingActionButton: MediaQuery.viewInsetsOf(context).bottom != 0
            //     ? null
            //     : const GeminiFloatingButton(),
            floatingActionButtonAnimator: FloatingActionButtonAnimator.scaling,
          ),
        );
      },
    );
  }
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;

  const _SelectedScreen({required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return const HomePage();
      case 1:
        return const HomeCategoriesScreen();
      case 2:
        return const OrdersScreen();

      case 3:
        return DashboardScreen();
    }

    return const SizedBox.shrink();
  }
}
