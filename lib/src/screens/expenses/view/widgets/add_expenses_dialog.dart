import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/expenses/controller/expenses_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/consts/api_strings.dart';
import '../../../../core/resources/app_spaces.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../shared/media/view_models/media_view_model.dart';
import '../../model/expenses_model.dart';

class AddExpensesDialog extends HookWidget {
  final ExpensesModel? expenses;

  const AddExpensesDialog({super.key, this.expenses});

  @override
  Widget build(BuildContext context) {
    final isEdit = expenses != null;
    final controllers = {
      ApiStrings.title: useTextEditingController(text: expenses?.title),
      ApiStrings.price:
          useTextEditingController(text: expenses?.price.toString()),
    };
    final mediaVM = context.read<MediaVM>();

    final projectFocusNode = useFocusNode();

    final formKey = useState(GlobalKey<FormState>());
    final isPaid = useState(expenses?.isPaid ?? true);

    useEffect(() {
      projectFocusNode.requestFocus();

      return () {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          mediaVM.clearFiles();
        });
      };
    }, []);

    return Consumer2<ExpensesVM, MediaVM>(
      builder: (context, expensesVM, mediaVM, child) {
        return BaseDialog(
            withCloseButton: true,
            isLoading: false,
            child: Form(
              key: formKey.value,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppSpaces.xSmallPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // * Header ===========================
                    Text(
                      context.tr.addExpenses,
                      style: context.title,
                    ),
                    context.largeGap,
                    SinglePickImageWidget(
                      pickedResult: mediaVM.filesPaths.firstOrNull,
                      networkImage: expenses?.image?.url,
                    ),
                    context.largeGap,

                    // * Title ===========================
                    BaseTextField(
                      title: context.tr.title,
                      hint: context.tr.title,
                      controller: controllers[ApiStrings.title],
                    ),

                    context.fieldsGap,

                    // *  Price ===========================
                    BaseTextField(
                      title: context.tr.price,
                      hint: context.tr.price,
                      textInputType: TextInputType.number,
                      controller: controllers[ApiStrings.price],
                    ),

                    context.fieldsGap,

                    Align(
                      alignment: Alignment.centerLeft,
                      child: Column(
                        children: [
                          Text(
                            context.tr.isPaid,
                            style: context.labelMedium,
                          ),
                          context.smallGap,
                          SwitchButtonWidget(
                              value: isPaid,
                              onChanged: (value) {
                                isPaid.value = value;
                              }),
                        ],
                      ),
                    ),
                    context.largeGap,

                    // * add product button ========================
                    Button(
                      isLoading: expensesVM.isLoading,
                      isOutLine: expensesVM.isLoading,
                      label: context.tr.add,
                      onPressed: () async {
                        if (!formKey.value.currentState!.validate()) return;
                        await expensesVM.addExpenses(context,
                            documentId: expenses?.documentId,
                            controllers: controllers,
                            filePath: mediaVM.filesPaths.firstOrNull ?? '',
                            isEdit: isEdit,
                            isPaid: isPaid.value);
                      },
                      isWhiteText: true,
                    )
                  ],
                ),
              ),
            ));
      },
    );
  }
}
