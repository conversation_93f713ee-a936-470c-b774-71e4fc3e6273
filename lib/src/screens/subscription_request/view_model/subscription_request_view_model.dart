import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/services/notifications_service.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/repository/auth_remote_repo.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/main_screen.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/model/subscription_request_model.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/repository/subscription_request_repository.dart';
import 'package:quickalert/quickalert.dart';

class SubscriptionRequestVM extends BaseVM {
  final SubscriptionRequestRepository subscriptionRequestRepository;
  final AuthRemoteRepository authRepository;

  SubscriptionRequestVM({
    required this.subscriptionRequestRepository,
    required this.authRepository,
  });

  // * Lists ========================
  List<SubscriptionRequestModel> subscriptionRequests = [];

  List<SubscriptionRequestModel> get pendingSubscriptionRequests =>
      subscriptionRequests.where((element) => !element.isApproved).toList();

  List<SubscriptionRequestModel> get approvedSubscriptionRequests =>
      subscriptionRequests.where((element) => element.isApproved).toList();

  List<SubscriptionRequestModel> get selectedSubscriptionList =>
      currentIndex == 0
          ? pendingSubscriptionRequests
          : approvedSubscriptionRequests;

  int currentIndex = 0;

  void setCurrentIndex(int index) {
    currentIndex = index;
    notifyListeners();
  }

  Future<void> createSubscriptionRequest(BuildContext context,
      {required SubscriptionRequestModel subscriptionRequest,
      required String fileResult}) async {
    return await baseFunction(
      context,
      () async {
        await subscriptionRequestRepository.createSubscriptionRequest(
            subscriptionRequest: subscriptionRequest, fileResult: fileResult);
        NotificationService.sendNotification(
          title: 'Payment Request 💰🎊',
          body:
              '${VendorModelHelper.currentVendorModel().name} ✅\n${subscriptionRequest.pricingModel?.name ?? ''} 💸 ${subscriptionRequest.pricingModel?.price ?? ''}L.E',
          userTokenOrTopic: AppConsts.adminTopic, //? Admins
          isTopic: true,
        );
      },
      additionalFunction: (context) {
        context.toReplacement(const MainScreen());
        QuickAlert.show(
          confirmBtnColor: ColorManager.primaryColor,
          title: context.tr.paymentSentSuccessfully,
          text: context.tr.yourRequestSentSuccessfully,
          context: context,
          type: QuickAlertType.success,
          confirmBtnText: context.tr.done,
        );
      },
    );
  }

  Future<void> approveSubscriptionRequest(
    BuildContext context, {
    required SubscriptionRequestModel subscriptionRequest,
    bool cannotRenew = true,
  }) async {
    return await baseFunction(
      context,
      () async {
        await subscriptionRequestRepository.approveSubscriptionRequest(
          subscriptionRequest: subscriptionRequest,
        );

        final copiedVendor = subscriptionRequest.vendor?.copyWith(
              startDate: cannotRenew ? null : DateTime.now(),
              expireDate: subscriptionRequest.pricingModel?.days == null ||
                      cannotRenew
                  ? null
                  : DateTime.now().add(
                      Duration(days: subscriptionRequest.pricingModel!.days!)),
              // vendorType: subscriptionRequest.vendor?.vendorType,
              paidAmount: subscriptionRequest.pricingModel?.price,
            ) ??
            const VendorModel();

        await authRepository.editVendor(
          vendor: copiedVendor,
          pricingDocId: subscriptionRequest.pricingModel?.documentId,
        );

        NotificationService.sendNotification(
          title: 'Subscription Request Approved 🎊',
          body: 'Your Subscription Request has been Approved 🎉',
          userTokenOrTopic: copiedVendor.businessName ?? '',
          isTopic: true,
        );

        NotificationService.sendNotification(
          title: 'Subscription Request Approved 🎊',
          body:
              '${copiedVendor.name} ✅\n${subscriptionRequest.pricingModel?.name ?? ''} 💸 ${subscriptionRequest.pricingModel?.price ?? ''}L.E',
          userTokenOrTopic: AppConsts.adminTopic, //? Admins
          isTopic: true,
        );
      },
    );
  }

  Future<void> makeAdminSubscriptionRequest(
    BuildContext context, {
    required SubscriptionRequestModel subscriptionRequest,
    String fileResult = '',
    required VendorType vendorType,
  }) async {
    return await baseFunction(
      context,
      () async {
        await subscriptionRequestRepository.createSubscriptionRequest(
          subscriptionRequest: subscriptionRequest,
          fileResult: fileResult,
          withOutVendor: true,
        );

        final copiedVendor = subscriptionRequest.vendor?.copyWith(
              documentId: subscriptionRequest.vendor?.documentId,
              vendorType: vendorType,
              startDate: DateTime.now(),
              expireDate: subscriptionRequest.pricingModel?.days == null
                  ? null
                  : DateTime.now().add(
                      Duration(days: subscriptionRequest.pricingModel!.days!)),
              paidAmount: subscriptionRequest.paidAmount,
            ) ??
            const VendorModel();

        await authRepository.editVendor(
          vendor: copiedVendor,
          pricingDocId: subscriptionRequest.pricingModel?.documentId,
        );

        NotificationService.sendNotification(
          title: 'New Admin Subscription Request 🎊',
          body:
              '${copiedVendor.name} ✅\n${subscriptionRequest.pricingModel?.name ?? ''} 💸 ${subscriptionRequest.pricingModel?.price ?? ''}L.E',
          userTokenOrTopic: AppConsts.adminTopic,
          isTopic: true,
        );
      },
    );
  }

  // * Get Subscription Requests
  Future<void> getSubscriptionRequests(BuildContext context) async {
    return await baseFunction(
      context,
      () async {
        subscriptionRequests =
            await subscriptionRequestRepository.getSubscriptionRequests();
      },
    );
  }

  // * Delete Subscription Request
  Future<void> deleteSubscriptionRequest(BuildContext context,
      {required String? documentId}) async {
    return await baseFunction(
      context,
      () async {
        await subscriptionRequestRepository
            .deleteSubscriptionRequest(documentId);
      },
    );
  }
}
