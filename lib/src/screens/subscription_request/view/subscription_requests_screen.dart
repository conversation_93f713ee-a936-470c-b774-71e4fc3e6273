import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/refresh_indictor/refresh_indictor_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_plans/view/widgets/plans_tab_bar_widget.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/view/add_new_subscription_dialog/add_new_subscription_dialog_widget.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/view/widgets/subscription_request_card.widget.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/view_model/subscription_request_view_model.dart';
import 'package:provider/provider.dart';

class SubscriptionRequestsScreen extends HookWidget {
  const SubscriptionRequestsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final readSubscriptionRequestVM = context.read<SubscriptionRequestVM>();
    final searchValue = useState('');

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        readSubscriptionRequestVM.getSubscriptionRequests(context);
      });

      return () {};
    }, []);

    return RefreshIndicatorWidget(
      onRefresh: () async {
        readSubscriptionRequestVM.getSubscriptionRequests(context);
      },
      child: Scaffold(
        floatingActionButton: FloatingActionButton(
          backgroundColor: ColorManager.primaryColor,
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) {
                return const AddNewSubscriptionDialog();
              },
            );
          },
          child: const Icon(
            Icons.add,
            color: Colors.white,
          ),
        ),
        appBar: MainAppBar(
          title: context.tr.subscriptionRequests,
          haveBackButton: true,
        ),
        body: Consumer<SubscriptionRequestVM>(
          builder: (context, subscriptionRequestVM, child) {
            final filteredList = subscriptionRequestVM.selectedSubscriptionList
                .where((element) =>
                    element.vendor!.name!
                        .toLowerCase()
                        .contains(searchValue.value.toLowerCase()) ||
                    element.vendor!.businessName!
                        .toLowerCase()
                        .contains(searchValue.value.toLowerCase()) ||
                    element.vendor!.email!
                        .toLowerCase()
                        .contains(searchValue.value.toLowerCase()) ||
                    element.vendor!.phone!
                        .toLowerCase()
                        .contains(searchValue.value.toLowerCase()))
                .toList();

            return Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SubscriptionPlansTapBarWidget(
                      tabs: [
                        context.tr.pending,
                        context.tr.approved,
                      ],
                      currentIndex: subscriptionRequestVM.currentIndex,
                      onTab: (index) {
                        subscriptionRequestVM.setCurrentIndex(index);
                      },
                    ),
                  ],
                ),
                context.xSmallGap,
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.mediumPadding,
                    vertical: AppSpaces.smallPadding,
                  ),
                  child: BaseTextField(
                    icon: const Icon(CupertinoIcons.search),
                    label: context.tr.search,
                    onChanged: (value) {
                      searchValue.value = value;
                    },
                  ),
                ),
                if (filteredList.isEmpty || subscriptionRequestVM.isLoading)
                  Expanded(
                    child: Center(
                      child: EmptyDataWidget(
                        isLoading: subscriptionRequestVM.isLoading,
                        message: context.tr.noSubscriptionRequests,
                      ),
                    ),
                  )
                else
                  Expanded(
                    child: ListView.separated(
                      padding: const EdgeInsets.only(
                        top: AppSpaces.mediumPadding,
                        right: AppSpaces.mediumPadding,
                        left: AppSpaces.mediumPadding,
                        bottom: AppSpaces.xlLargePadding,
                      ),
                      separatorBuilder: (context, index) => context.mediumGap,
                      itemCount: filteredList.length,
                      itemBuilder: (context, index) {
                        final subscriptionRequest = filteredList[index];
                        return SubscriptionCardWidget(
                          subscriptionRequest: subscriptionRequest,
                        );
                      },
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
