import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/reports/model/report_model.dart';
import 'package:idea2app_vendor_app/src/screens/reports/repository/report_repository.dart';

class ReportVM extends BaseVM {
  final ReportRepository reportRepository;

  ReportVM({required this.reportRepository});

  ReportModel? report;

  Future<void> getReports(BuildContext context,
      {required DateTime startDate, required DateTime? endDate}) async {
    return await baseFunction(context, () async {
      report = await reportRepository.getReports(
        startDate: startDate,
        endDate: endDate,
      );
    });
  }

  Future<void> printReport(
    BuildContext context, {
    required String date,
    required ReportModel report,
  }) async {
    return await baseFunction(context,() async {
       reportRepository.printReport(
        context,
        date: date,
        report: report,
      );
    });
  }
}
