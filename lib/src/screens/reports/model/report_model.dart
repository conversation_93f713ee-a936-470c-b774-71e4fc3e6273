import 'package:idea2app_vendor_app/src/core/utils/logger.dart';

dynamic responseFromReportModel(dynamic response) {
  Log.w('afsffasaf $response');

  final reportData = ReportModel.fromJson(response);

  return reportData;
}

class ReportModel {
  final int totalCount;
  final num totalEarnings;
  final List<DailyEarningsModel> dailyEarnings;

  ReportModel({
    required this.totalCount,
    required this.totalEarnings,
    this.dailyEarnings = const [],
  });

  factory ReportModel.fromJson(Map<String, dynamic> json) {
    return ReportModel(
      totalCount: json['total_count'],
      totalEarnings: json['total_earnings'],
      dailyEarnings: json['daily_earnings'] != null
          ? (json['daily_earnings'] as List)
              .map((e) => DailyEarningsModel.fromJson(e))
              .toList()
          : [],
    );
  }
}

class DailyEarningsModel {
  final DateTime date;
  final int earnings;

  DailyEarningsModel({
    required this.date,
    required this.earnings,
  });

  factory DailyEarningsModel.fromJson(Map<String, dynamic> json) {
    return DailyEarningsModel(
      date: DateTime.parse(json['date']),
      earnings: json['earnings'],
    );
  }
}
