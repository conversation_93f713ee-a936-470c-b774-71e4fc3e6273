import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/services/notifications_service.dart';
import 'package:idea2app_vendor_app/src/screens/alert_screen.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/login/login.screen.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/main_screen.dart';
import 'package:provider/provider.dart';

import 'core/data/local/local_keys.dart';
import 'core/data/local/shared_preferences/get_storage.dart';
import 'screens/auth/view_model/auth_view_model.dart';
import 'screens/settings/view_model/settings_view_model.dart';

final GlobalKey<NavigatorState> mainScreenNavigatorKey =
    GlobalKey<NavigatorState>();

class SelectedScreen extends HookWidget {
  const SelectedScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final vendorData =
        GetStorageHandler.getLocalData(key: LocalKeys.vendorData);

    final isLoggedIn = vendorData != null;

    final catVM = context.read<CategoryVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (isLoggedIn) {
          unawaited(catVM.getCategories(context));
        }
      });
      return () {};
    }, []);

    return Consumer2<AuthVM, SettingsVM>(
      builder: (context, authVM, settingsVM, child) {
        final isActive = authVM.isActive;

        Widget navigateWidget() {
          if (isActive) {
            if (isLoggedIn) {
              return const BaseScreen();
            } else {
              return const LoginScreen();
            }
          } else {
            return const AlertSubscriptionWidget();
          }
        }

        return navigateWidget();
      },
    );
  }
}

class BaseScreen extends HookWidget {
  const BaseScreen({super.key});

  @override
  Widget build(BuildContext context) {
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        //! Get Notifications If App Is Opened
        NotificationService.showNotifications(context);
      });

      return () {};
    }, []);

    return const MainScreen();
  }
}
